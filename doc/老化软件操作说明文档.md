﻿﻿# 老化软件操作说明文档



## 1 老化软件主界面

![main_window](img/main_window_ui.png)



## 2 老化原理

- 配置雷达相关寄存器,使雷达进入要求的运行状态.
- 读取雷达寄存器,并进行计算分析.
- 根据设定的寄存器数据上下限阈值,判断寄存器数据是否合格.
- 当存在任一项数据不合格时,判定为老化失败,需对雷达进行分析



## 3 软件操作
步骤：

- [ ] 登录
- [ ] 取消勾选MES过站、数据请求、MES上传
- [ ] 设置保存路径
- [ ] 接入雷达，雷达此时初始IP必须为*************，msop端口为6699
- [ ] 双击序号
- [ ] 写入SN号，写入项目代码，确认左右是否为FL
- [ ] 点击开始老化
- [ ] 等待老化状态变为`忙碌`，即开始老化

### 3.1 登录

软件的各项操作都要求登录，有相应权限才能进入下一步测试

在主窗口点击 `设置->MES设置`进入登录页面

<img src="img/login_ui.png" alt="login" style="zoom: 80%;" />

**第一次**登录需要联系开发者，索要`开发密码`，进行登录，登录成功后在本机添加管理员账户，后续即可直接使用管理员账户进行登录，或者添加技术员登录操作

注：开发者密码每日会更换，MES窗口需要一直保持，不能关闭



由于目前还没上MES，因此需要**取消勾选**`MES过站`、`数据请求`和`MES上传`

<img src="img/mes_uncheck.png" alt="image-20230814120019942" style="zoom:80%;" />

需要设置结果的保存路径，则是在上图中，”保存路径“中进行设置





### 3.2 参数配置

打开设置，找到老化参数设置

![image-20230814120309991](img/aging_settings.png)

- 登录管理员及以上权限,可以配置老化参数,技术员权限可以查看参数.

- 点击设置-老化参数设置,打开参数配置界面.
- 选择要配置的参数,输入数据,关闭参数界面,参数即被保存.
- 参数配置后,建议**重启**软件.

### 3.3 开始老化

1. 保证接上的雷达IP为`*************`
2. 手动启动
   - 双击将要老化的雷达所在老化架对应的序号,进入启动老化界面.
   - 输入雷达SN,选择雷达对应的项目编号,安装位置等.
   - 点击"开始老化",进入老化流程.

![image-20230814120611793](img/start_aging.png)

选择好项目代码如`0320`即可开始，`IP`和`MSOP`在默认情况下无需修改。左右则表示放置方位，如没有方位则选FL



开始老化时，首先会连接上`*************`的雷达，并将雷达ip修改为货架所对应IP默认，如上图，1序号将修改为`*************`，2序号将被修改为`*************`以此类推



由于雷达初始IP都为`*************`，在修改IP阶段只能接入一个雷达，避免两个相同IP为`*************`同时接入到网络出现冲突。



当状态变为`忙碌`的时候即可接入下个雷达进行老化



### 3.4 结果确认

![](img/ok_pass.png)

- 老化一共有4个状态,空闲(蓝色),忙碌(黄色),失败(红色),通过(绿色).
- 空闲--当对应编号的老化位置没有老化任务时.
- 忙碌--当对应编号的老化位置正在进行老化任务.
- 失败--当对应编号的老化位置老化失败时,MES或雷达有异常.
- 通过--当对应编号的老化位置老化完成,雷达无异常时.

## 其他问题

### SN与项目编号

出现

> SN与项目编号、安装位置的规则不符，或者从MES获取错误，如不需要检测SN请在MES界面设置不校验SN项目

请检查开始老化的窗口，是否正确设置了项目代码和“左右”，这两者都不允许为`not_found`

可以选择屏蔽SN的检查，在`设置->MES设置->不校验SN项目`中设置，以分号`;`相隔示例参考

> 0320;0321;0303;0335

### 校验文件失败

每当配置文件`csv`发生变化的时候，测试校验都会失败，需要检查软件安装位置的配置文件是否是正常变更，还是其他软件导致的变更。如是正常变更，则点击重新校验文件

`设置->MES设置->高级设置->重置配置文件校验值`

### 存在不满足生产条件

如出现不满足生产条件的情况

可以检查保存路径是否正确或者更换保存路径
`设置->MES设置->保存路径`中进行设置
