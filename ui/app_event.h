﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef APP_EVENT_H
#define APP_EVENT_H

#include "common_struct.h"
#include "data_struct.h"
#include <QJsonObject>
#include <QObject>
#include <memory>

class MesWidget;

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
class CsvUtils;
class MainWindow;
namespace rsfsc_lib
{
class WidgetLogSetting;
}  // namespace rsfsc_lib

using ErrorT = uint64_t;
enum ErrorType : ErrorT
{
  //  error for all projects
  //  no more than 32 types
  ERROR_TYPE_NO_ERROR               = 0,
  ERROR_TYPE_LIDAR_PARAM_PATH_WRONG = 1UL,
  ERROR_TYPE_PCAP_WRONG             = 1UL << 1U,
  ERROR_TYPE_IP_WRONG               = 1UL << 2U,
  ERROR_TYPE_DRIVER_ALREADY_START   = 1UL << 3U,
  ERROR_TYPE_LIDAR_CONNECT_FAILED   = 1UL << 4U,
  ERROR_TYPE_LIDAR_NOT_CONNECT      = 1UL << 5U,
  ERROR_TYPE_LIDAR_NAME             = 1UL << 6U,

  //  error specify for your project
  ERROR_TYPE_CALIB_FAILED           = 1UL << 32U,
  ERROR_TYPE_ROTATOR_CONNECT_FAILED = 1UL << 33U,
  ERROR_TYPE_ROTATOR_NOT_CONNECTED  = 1UL << 34U,
  ERROR_TYPE_CONFIG_LOAD_WRONG      = 1UL << 35U,
  ERROR_TYPE_LIDAR_READ_WRITE_WRONG = 1UL << 36U,
  ERROR_TYPE_SAVE_PATH_WRONG        = 1UL << 37U,
  ERROR_TYPE_DATA_NOT_COLLECT       = 1UL << 38U,
  ERROR_TYPE_DATA_FORMAT_WRONG      = 1UL << 39U,
  ERROR_TYPE_RESULT_WRONG           = 1UL << 40U,
  ERROR_TYPE_ROTATE_TIMEOUT         = 1UL << 41U,
  ERROR_TYPE_LOG_NOT_SET            = 1UL << 42U,
  ERROR_TYPE_SAVE_LOG_FAILED        = 1UL << 43U,
  ERROR_TYPE_PROCEDURE_CHECK_FAILED = 1UL << 44U,
  ERROR_TYPE_PLC_CONNECT_FAILED     = 1UL << 45U,
  ERROR_TYPE_PLC_TIMEOUT            = 1UL << 46U,
  ERROR_TYPE_PLC_RUN_TIMEOUT        = 1UL << 47U,
  ERROR_TYPE_PLC_NOT_CONNECTED      = 1UL << 48U,
  ERROR_TYPE_JIG_NOT_CONNECTED      = 1UL << 49U,
  ERROR_TYPE_JIG_RESET_TIMEOUT      = 1UL << 50U

};

inline void addErrorType(ErrorType& _org, const ErrorType& _et2add) { _org = ErrorType(_org | _et2add); }

inline void deleteErrorType(ErrorType& _org, const ErrorType& _et2delete) { _org = ErrorType(~_et2delete & _org); }

class AppEvent : public QObject
{
  Q_OBJECT
public:
  static AppEvent* getInstance()
  {
    static AppEvent instance;
    return &instance;
  }

Q_SIGNALS:

  /****************************************************************
   * RULES:
   * 1. use "update" but not "change"          
  ****************************************************************/
  //  signal that all ui should connect
  void signalUpdateNameSpace(const QString&);
  void signalSaveParam();

  //  signal that all ui can connect
  void signalShowInfoText(const QString& _msg);
  void signalShowWarningText(const QString& _msg);
  void signalShowErrorText(const QString& _msg);
  void signalShowErrorMsg(ErrorT _error_type);
  void signalShowInfoVariant(const QString& _name, const QVariant& _value);
  void signalShowErrorMessageBox(const QString& _msg);

  // 人工确认
  void signalHumanConfirmResult(const QString& _ver_file_path, HumanConfirmResult* _msg_result);

  //  signal that only for specify ui
  void signalUpdateProgress(const float _current_progress);
  void signalUpdateTemperature(const double _current_temperature);
  void signalUpdateFPS(const double _current_fps);
  void signalUpdateProtocolType(int);
  void signalUpdateLidarParamPath(const QString&);
  void signalUpdateMarker();
  void signalUpdateAdminState();

  void signalFsmStarting(int _index);
  void signalFsmStarted(int _index);
  void signalFsmStopping(int _index);
  void signalFsmStopped(int _index);

  void signalUpdateParaInfo(const int _lidar_index);

  void signalUpdateRelayState(int _lidar_index, bool _is_connected);

  void signalLidarChangeIpIsReady(const bool _is_ready, const int _lidar_index);

public:
  void setVersionStr(const QString& _version_str) { version_str_ = _version_str; }
  [[nodiscard]] QString getVersionStr() const { return version_str_; }

  void setMesWidget(MesWidget* _mes_widget) { mes_widget_ = _mes_widget; }
  [[nodiscard]] MesWidget* getMesWidget() const { return mes_widget_; }

  [[nodiscard]] rsfsc_lib::WidgetLogSetting* getWidgetLogSetting() const;
  std::shared_ptr<CsvUtils> getCsvUtils(const QString& _project_str)
  {
    if (csv_parser_map_.find(_project_str.toStdString()) == csv_parser_map_.end())
    {
      return nullptr;
    }
    return csv_parser_map_[_project_str.toStdString()];
  }
  void setCsvUtils(const QString& _project_str, std::shared_ptr<CsvUtils>& _csv_parser)
  {
    csv_parser_map_[_project_str.toStdString()] = std::move(_csv_parser);
  }

  void setMainWindow(MainWindow* _main_window) { main_window_ = _main_window; }
  [[nodiscard]] MainWindow* getMainWindow() const { return main_window_; }

  void setChangingIpIndex(int _index) { changing_ip_index_.store(_index); }
  [[nodiscard]] int getChangingIpIndex() const { return changing_ip_index_.load(); }

  ParaInfo getParaInfo();
  void setAppConfig(const QJsonObject& _app_config) { app_config_ = _app_config; }
  [[nodiscard]] QJsonObject getAppConfig() const { return app_config_; }

private:
  explicit AppEvent(QObject* /*_parent*/ = nullptr) {};

public:
  AppEvent(const AppEvent&) = delete;
  AppEvent& operator=(const AppEvent&) = delete;
  ~AppEvent() override                 = default;

private:
  MesWidget* mes_widget_ = nullptr;
  std::map<std::string, std::shared_ptr<CsvUtils>> csv_parser_map_;

  MainWindow* main_window_ = nullptr;

  QJsonObject app_config_;

  std::atomic<int> changing_ip_index_ {};
  QString version_str_;
};

inline AppEvent* app() { return AppEvent::getInstance(); }

}  // namespace lidar
}  // namespace robosense

#endif  // _APP_EVENT_H_