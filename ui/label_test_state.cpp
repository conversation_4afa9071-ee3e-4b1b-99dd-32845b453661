﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "label_test_state.h"

#include <QtWidgets/QSizePolicy>

namespace robosense
{
namespace lidar
{

LabelTestState::LabelTestState(QWidget* _parent) : QLabel(_parent)
{
  QSizePolicy size_policy = this->sizePolicy();
  // size_policy.setHorizontalPolicy(QSizePolicy::Expanding);
  size_policy.setVerticalPolicy(QSizePolicy::Expanding);
  this->setSizePolicy(size_policy);
  this->setAlignment(Qt::AlignCenter);

  setState(TEST_STATE_NOT_START);
}

LabelTestState::~LabelTestState() = default;

void LabelTestState::setState(const TestState _ts)
{
  QFont font;
  font.setWeight(QFont::Bold);
  switch (_ts)
  {
  case TEST_STATE_NOT_START:
    this->setText("未开始");
    this->setStyleSheet("background-color: rgb(255, 255, 255);color: rgb(0, 0, 0);");
    break;
  case TEST_STATE_RUNNING:
    this->setText("测试中");
    this->setStyleSheet("background-color: rgb(155, 0, 155);color: rgb(100, 255, 100);");
    break;
  case TEST_STATE_FAILED:
    this->setText("测试失败");
    this->setStyleSheet("background-color: rgb(255, 0, 0);color: rgb(0, 255, 255);");
    break;
  case TEST_STATE_PASS:
    this->setText("测试成功");
    this->setStyleSheet("background-color: rgb(0, 128, 0);color: rgb(0, 0, 0);");
    break;

  default: break;
  }
  this->setFont(font);
  current_test_state_ = _ts;
}

}  // namespace lidar
}  // namespace robosense