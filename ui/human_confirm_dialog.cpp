﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "human_confirm_dialog.h"
#include "rapidcsv/rapidcsv.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QDebug>
#include <QHeaderView>
#include <QMessageBox>
#include <QVBoxLayout>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

HumanConfirmDialog::HumanConfirmDialog(QWidget* _parent) : QDialog(_parent)
{
  setupUi();
  setupConnections();
}

void HumanConfirmDialog::setupUi()
{
  setWindowTitle("人工确认");
  resize(800, 600);

  QVBoxLayout* layout = new QVBoxLayout(this);

  table_widget_ = new QTableWidget(this);
  table_widget_->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
  layout->addWidget(table_widget_);

  QDialogButtonBox* button_box = new QDialogButtonBox(this);
  agree_button_                = button_box->addButton("一致", QDialogButtonBox::AcceptRole);
  disagree_button_             = button_box->addButton("不一致", QDialogButtonBox::RejectRole);
  layout->addWidget(button_box);
}

void HumanConfirmDialog::setupConnections()
{
  connect(agree_button_, &QPushButton::clicked, [this]() {
    confirmed_ = true;
    accept();
  });

  connect(disagree_button_, &QPushButton::clicked, [this]() {
    confirmed_ = false;
    reject();
  });
}

bool HumanConfirmDialog::loadCsv(const QString& _file_path)
{
  try
  {
    rapidcsv::Document doc(_file_path.toStdString(), rapidcsv::LabelParams(0, -1));
    size_t row_count = doc.GetRowCount();
    size_t col_count = doc.GetColumnCount();

    table_widget_->setRowCount(static_cast<int>(row_count));
    table_widget_->setColumnCount(static_cast<int>(col_count));

    // 设置表头
    for (size_t col = 0; col < col_count; ++col)
    {
      std::string col_name = doc.GetColumnName(col);
      table_widget_->setHorizontalHeaderItem(static_cast<int>(col),
                                             new QTableWidgetItem(QString::fromStdString(col_name)));
    }

    // 填充内容
    for (size_t row = 0; row < row_count; ++row)
    {
      for (size_t col = 0; col < col_count; ++col)
      {
        std::string cell       = doc.GetCell<std::string>(static_cast<int>(col), static_cast<int>(row));
        QTableWidgetItem* item = new QTableWidgetItem(QString::fromStdString(cell));

        // 结果列（假设是第5列）
        if (col == 5)
        {
          QString result = QString::fromStdString(cell).trimmed().toLower();
          if (result == "fail")
          {
            item->setBackground(Qt::red);
          }
          else if (result == "pass")
          {
            item->setBackground(Qt::green);
          }
        }

        table_widget_->setItem(static_cast<int>(row), static_cast<int>(col), item);
      }
    }

    table_widget_->resizeColumnsToContents();
    return true;
  }
  catch (const std::exception& e)
  {
    QMessageBox::critical(this, "错误", QString("无法读取CSV文件: %1").arg(e.what()));
    return false;
  }
}

bool HumanConfirmDialog::isConfirmed() const { return confirmed_; }

}  // namespace lidar
}  // namespace robosense