#!/usr/bin/env bash
# /******************************************************************************
#  * Copyright 2024 RoboSense All rights reserved.
#  * Suteng Innovation Technology Co., Ltd. www.robosense.ai
#  *
#  * This software is provided to you directly by RoboSense and might
#  * only be used to access RoboSense LiDAR. Any compilation,
#  * modification, exploration, reproduction and redistribution are
#  * restricted without RoboSense's prior consent.
#  *
#  * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
#  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
#  * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
#  * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
#  * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
#  * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
#  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
#  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
#  * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
#  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  * POSSIBILITY OF SUCH DAMAGE.
#  *****************************************************************************/

# 获取输入参数，如果为空则使用当前目录
directory="${1:-.}"

# 检查目录是否存在
if [ ! -d "$directory" ]; then
    echo "Directory $directory does not exist."
    exit 1
fi

# 切换到目标目录
cd "$directory" || exit

# 创建md5sum文件并计算文件的MD5校验和
find ./ -path './.git' -prune -o -type f ! -name 'md5sum.txt' -print0 | xargs -0 md5sum | sort > md5sum.txt
find ./ -path './.git' -prune -o -type l ! -name 'md5sum.txt' -print0 | xargs -r -0 md5sum | sort >> md5sum.txt

echo "MD5 checksums have been written to $directory/md5sum.txt"
