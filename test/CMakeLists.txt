﻿project(test_main LANGUAGES CXX)
find_package(Threads REQUIRED)
find_package(
  Qt5
  COMPONENTS Core
  REQUIRED)

# If GoogleTest is not found, fetch it using FetchContent
find_package(GTest QUIET)
if(NOT GTest_FOUND)
  include(FetchContent)
  FetchContent_Declare(
    googletest
    GIT_REPOSITORY ***********************:system_codebase/factory_tool/common_lib/googletest.git
    GIT_TAG release-1.12.1
    GIT_SHALLOW TRUE
    GIT_DEPTH 1
    GIT_CONFIG advice.detachedHead=false)

  # Download and configure GoogleTest
  # FetchContent_MakeAvailable(googletest)
  FetchContent_GetProperties(googletest)
  if(NOT googletest_POPULATED)
    FetchContent_Populate(googletest)
    add_subdirectory(${googletest_SOURCE_DIR} ${googletest_BINARY_DIR} EXCLUDE_FROM_ALL)
  endif()
endif()

add_executable(${PROJECT_NAME} test.cpp)

# 添加包含目录，以便测试可以访问主项目的头文件
target_include_directories(${PROJECT_NAME} PRIVATE ../include)
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE ../lib/rsfsc_lib/include ../lib/rsfsc_lib/ui ../lib)
target_include_directories(${PROJECT_NAME} PRIVATE ../src ../src/lidar_data_read/ ../ui)

# 链接静态库和测试库
target_link_libraries(
  ${PROJECT_NAME} mech_aging_lib # 链接主项目的静态库
  Qt5::Core # 链接 Qt Core 库
  gtest gtest_main Threads::Threads)
