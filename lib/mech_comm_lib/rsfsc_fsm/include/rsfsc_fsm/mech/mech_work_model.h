﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_WORK_MODEL_H
#define MECH_WORK_MODEL_H

#include "lidar_manager.h"
#include "rsfsc_fsm/work_model.h"
#include <string>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace rsfsc_lib
{
class WidgetLidarInfo;
class WidgetLogSetting;
}  // namespace rsfsc_lib

class MechWorkModel : public WorkModel
{
public:
  explicit MechWorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info) : WorkModel(_lidar_info) {}
  MechWorkModel(const MechWorkModel&) = delete;
  MechWorkModel(MechWorkModel&&)      = delete;
  MechWorkModel& operator=(const MechWorkModel&) = delete;
  MechWorkModel& operator=(MechWorkModel&&) = delete;
  ~MechWorkModel() override;

  [[nodiscard]] int getLidarIndex() override;

  void abort() override;
  bool isAbort() override;

  bool addMeasureMessage(const std::string& _name, const bool _data);
  bool addMeasureMessage(const std::string& _name, const double _data);
  bool addMeasureMessage(const std::string& _name, const float _data);
  bool addMeasureMessage(const std::string& _name, const int _data);
  bool addMeasureMessage(const std::string& _name, const uint32_t _data);
  bool addMeasureMessage(const std::string& _name, const std::string& _data);
  bool addMeasureMessage(const std::string& _name, const QVariant& _value);

  /**
   * @brief     通过寄存器csv读取单个寄存器数据
   * 
   * @param     _key               
   * @param     _data              
   * @param     _timeout           
   * @return    true               
   * @return    false              
  **/
  bool readRegDataByKey(const QString& _key, uint32_t& _data, const int _timeout = 8000);
  /**
   * @brief     通过寄存器csv读取顶板寄存器，可添加字节大小，自动拼接
   * 
   * @param     _key               
   * @param     _data              
   * @param     _timeout           
   * @return    true               
   * @return    false              
  **/
  bool readTopRegDataByKey(const QString& _key, uint32_t& _data, const uint32_t _byte_size, const int _timeout = 8000);

  bool writeRegDataByKey(const QString& _key, const uint32_t _data);
  bool writeRegDataByKeyWithVer(const QString& _key, const uint32_t _data);
  bool writeCsvData(const QString& _key);
  bool writeCsvDataAfterCalib(const QString& _key);

  LimitInfo getLimitInfo(const std::string& _name);
  virtual rsfsc_lib::WidgetLogSetting* getWidgetLogSetting() = 0;

  void setFailMsg(const std::string& _fail_msg) { fail_msg_ = _fail_msg; }
  [[nodiscard]] const std::string& getFailMsg() const { return fail_msg_; }

  // 具体功能
  bool startUpOpticalErrorTest();
  bool stopUpOpticalErrorTest();
  bool getUpOpticalError(uint32_t& _error, uint32_t& _total);
  bool startDownOpticalErrorTest();
  bool stopDownOpticalErrorTest();
  bool getDownOpticalError(uint32_t& _error, uint32_t& _total);
  void getOpticalErrorRate(double& _up_error_rate, double& _down_error_rate) const;

private:
  std::string fail_msg_;
  rsfsc_lib::WidgetLogSetting* widget_log_setting_ptr_ = nullptr;

  // 具体功能
  double optical_up_error_rate_   = -1;
  double optical_down_error_rate_ = -1;
};

}  // namespace lidar
}  // namespace robosense
#endif  // MECH_WORK_MODEL_H