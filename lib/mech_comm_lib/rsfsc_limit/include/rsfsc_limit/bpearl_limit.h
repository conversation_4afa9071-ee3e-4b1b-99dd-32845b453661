#pragma once
#include <limits>
// Auto-generated from bpearl_limit.csv
namespace robosense {
namespace lidar {
namespace limit {
namespace bpearl {

constexpr const char* top_2v5 = "top_2v5";
constexpr const char* top_hv = "top_hv";
constexpr const char* top_n3v3 = "top_n3v3";
constexpr const char* top_tx3v8 = "top_tx3v8";
constexpr const char* top_vbus = "top_vbus";
constexpr const char* bot_12v = "bot_12v";
constexpr const char* bot_5v = "bot_5v";
constexpr const char* bot_fpga_1v = "bot_fpga_1v";
constexpr const char* machine_vbus = "machine_vbus";
constexpr const char* bot_curr = "bot_curr";
constexpr const char* top_fpga_temp = "top_fpga_temp";
constexpr const char* bot_fpga_temp = "bot_fpga_temp";
constexpr const char* top_under_temp = "top_under_temp";
constexpr const char* apd_temp = "apd_temp";
constexpr const char* real_speed = "real_speed";
constexpr const char* stress_test = "stress_test";
constexpr const char* upload_error_rate = "upload_error_rate";
constexpr const char* download_error_rate = "download_error_rate";
}  // namespace bpearl
}  // namespace limit
}  // namespace lidar
}  // namespace robosense