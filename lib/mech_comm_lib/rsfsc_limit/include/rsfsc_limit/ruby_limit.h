#pragma once
#include <limits>
// Auto-generated from ruby_limit.csv
namespace robosense {
namespace lidar {
namespace limit {
namespace ruby {

constexpr const char* apd_temp = "apd_temp";
constexpr const char* top_under_temp = "top_under_temp";
constexpr const char* top_above_temp = "top_above_temp";
constexpr const char* top_fpga_temp = "top_fpga_temp";
constexpr const char* bot_fpga_temp = "bot_fpga_temp";
constexpr const char* top_2v5 = "top_2v5";
constexpr const char* top_vbus = "top_vbus";
constexpr const char* top_tx5v = "top_tx5v";
constexpr const char* top_a5v = "top_a5v";
constexpr const char* top_hv = "top_hv";
constexpr const char* top_n5v = "top_n5v";
constexpr const char* machine_vbus = "machine_vbus";
constexpr const char* bot_5v = "bot_5v";
constexpr const char* bot_28v = "bot_28v";
constexpr const char* cbus_curr = "cbus_curr";
constexpr const char* real_speed = "real_speed";
constexpr const char* stress_test = "stress_test";
constexpr const char* upload_error_rate = "upload_error_rate";
constexpr const char* download_error_rate = "download_error_rate";
}  // namespace ruby
}  // namespace limit
}  // namespace lidar
}  // namespace robosense