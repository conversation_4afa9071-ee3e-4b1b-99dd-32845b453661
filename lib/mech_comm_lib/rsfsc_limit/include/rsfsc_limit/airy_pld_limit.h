#pragma once
#include <limits>
// Auto-generated from airy_pld_limit.csv
namespace robosense {
namespace lidar {
namespace limit {
namespace airy {

constexpr const char* fsm_ping = "fsm_ping";
constexpr const char* fsm_connect_before = "fsm_connect_before";
constexpr const char* fsm_connect_after = "fsm_connect_after";
constexpr const char* fsm_change_ip = "fsm_change_ip";
constexpr const char* fsm_firmware_update_unzip = "fsm_firmware_update_unzip";
constexpr const char* fsm_firmware_update_app = "fsm_firmware_update_app";
constexpr const char* fsm_firmware_update_bot = "fsm_firmware_update_bot";
constexpr const char* fsm_firmware_update_motor_speed = "fsm_firmware_update_motor_speed";
constexpr const char* fsm_firmware_update_top = "fsm_firmware_update_top";
constexpr const char* fsm_firmware_update_write_config = "fsm_firmware_update_write_config";
constexpr const char* fsm_firmware_update_reboot = "fsm_firmware_update_reboot";
constexpr const char* fsm_firmware_update_check = "fsm_firmware_update_check";
constexpr const char* fsm_difop_timeout = "fsm_difop_timeout";
constexpr const char* fsm_connect_lidar = "fsm_connect_lidar";
constexpr const char* fsm_stress_success_count = "fsm_stress_success_count";
constexpr const char* fsm_init_lidar = "fsm_init_lidar";
constexpr const char* fsm_init_read_data = "fsm_init_read_data";
constexpr const char* fsm_init_stress_test = "fsm_init_stress_test";
constexpr const char* fsm_lidar_sn_match = "fsm_lidar_sn_match";
constexpr const char* fsm_bound_ip_pool = "fsm_bound_ip_pool";
constexpr const char* fsm_msop_channel = "fsm_msop_channel";
constexpr const char* fsm_restore_ip = "fsm_restore_ip";
constexpr const char* fsm_encod_calib_motor_stable = "fsm_encod_calib_motor_stable";
constexpr const char* fsm_encod_calib_collect = "fsm_encod_calib_collect";
constexpr const char* fsm_encod_calib_process = "fsm_encod_calib_process";
constexpr const char* fsm_encod_calib_check = "fsm_encod_calib_check";
constexpr const char* fsm_encod_calib_write = "fsm_encod_calib_write";
constexpr const char* fsm_encod_calib_save = "fsm_encod_calib_save";
constexpr const char* fsm_encod_calib_read = "fsm_encod_calib_read";
constexpr const char* fsm_encod_calib_read_check = "fsm_encod_calib_read_check";
constexpr const char* fsm_up_light_test_en = "fsm_up_light_test_en";
constexpr const char* fsm_up_light_test_total = "fsm_up_light_test_total";
constexpr const char* fsm_up_light_test_error = "fsm_up_light_test_error";
constexpr const char* fsm_up_light_test_error_rate = "fsm_up_light_test_error_rate";
constexpr const char* fsm_down_light_test_en = "fsm_down_light_test_en";
constexpr const char* fsm_down_light_test_total = "fsm_down_light_test_total";
constexpr const char* fsm_down_light_test_error = "fsm_down_light_test_error";
constexpr const char* fsm_down_light_test_error_rate = "fsm_down_light_test_error_rate";
constexpr const char* fsm_light_test_state = "fsm_light_test_state";
constexpr const char* fsm_up_error_rate = "fsm_up_error_rate";
constexpr const char* fsm_down_error_rate = "fsm_down_error_rate";
constexpr const char* fsm_state = "fsm_state";
constexpr const char* reboot_lidar = "reboot_lidar";
constexpr const char* require_ver_angle = "require_ver_angle";
constexpr const char* ver_angle_error = "ver_angle_error";
constexpr const char* write_chn_angle = "write_chn_angle";
constexpr const char* check_chn_angle = "check_chn_angle";
constexpr const char* require_customer_info = "require_customer_info";
constexpr const char* mes_mac_address = "mes_mac_address";
constexpr const char* require_vbd_vol = "require_vbd_vol";
constexpr const char* vbd_voltage = "vbd_voltage";
constexpr const char* vbd_read_data = "vbd_read_data";
constexpr const char* vbd_write_data = "vbd_write_data";
constexpr const char* vbd_check_data = "vbd_check_data";
constexpr const char* vbd_calib_status = "vbd_calib_status";
constexpr const char* clear_calib_data = "clear_calib_data";
constexpr const char* write_exp_zero_angle = "write_exp_zero_angle";
constexpr const char* top_version_before = "top_version_before";
constexpr const char* bot_version_before = "bot_version_before";
constexpr const char* app_version_before = "app_version_before";
constexpr const char* motor_version_before = "motor_version_before";
constexpr const char* check_top_firmware_version = "check_top_firmware_version";
constexpr const char* check_bot_firmware_version = "check_bot_firmware_version";
constexpr const char* check_app_firmware_version = "check_app_firmware_version";
constexpr const char* check_motor_firmware_version = "check_motor_firmware_version";
constexpr const char* check_config_firmware_version = "check_config_firmware_version";
constexpr const char* human_confirm = "human_confirm";
constexpr const char* motor_set_speed = "motor_set_speed";
constexpr const char* top_firmware_version = "top_firmware_version";
constexpr const char* bot_firmware_version = "bot_firmware_version";
constexpr const char* app_firmware_version = "app_firmware_version";
constexpr const char* motor_firmware_version = "motor_firmware_version";
constexpr const char* realtime_speed = "realtime_speed";
constexpr const char* bot_vbus = "bot_vbus";
constexpr const char* bot_ibus = "bot_ibus";
constexpr const char* bot_sys_5v = "bot_sys_5v";
constexpr const char* bot_sys_12v = "bot_sys_12v";
constexpr const char* bot_vcco_psio0 = "bot_vcco_psio0";
constexpr const char* bot_sys_1v2 = "bot_sys_1v2";
constexpr const char* bot_vcco_psddr = "bot_vcco_psddr";
constexpr const char* top_vbus = "top_vbus";
constexpr const char* top_sys_3v8 = "top_sys_3v8";
constexpr const char* top_sys_3v3 = "top_sys_3v3";
constexpr const char* top_sys_2v5 = "top_sys_2v5";
constexpr const char* top_sys_1v1 = "top_sys_1v1";
constexpr const char* top_rx_vbd = "top_rx_vbd";
constexpr const char* top_tx_charge = "top_tx_charge";
constexpr const char* top_sys_1v8 = "top_sys_1v8";
constexpr const char* top_sys_1v0 = "top_sys_1v0";
constexpr const char* bot_psintlp = "bot_psintlp";
constexpr const char* bot_psintfp = "bot_psintfp";
constexpr const char* bot_ps_aux = "bot_ps_aux";
constexpr const char* bot_pl_vccint = "bot_pl_vccint";
constexpr const char* total_power = "total_power";
}  // namespace airy
}  // namespace limit
}  // namespace lidar
}  // namespace robosense