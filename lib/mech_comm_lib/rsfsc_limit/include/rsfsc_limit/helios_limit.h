#pragma once
#include <limits>
// Auto-generated from helios_limit.csv
namespace robosense {
namespace lidar {
namespace limit {
namespace helios {

constexpr const char* apd_avg_temp = "apd_avg_temp";
constexpr const char* top_under_temp = "top_under_temp";
constexpr const char* top_fpga_temp = "top_fpga_temp";
constexpr const char* bot_fpga_temp = "bot_fpga_temp";
constexpr const char* top_5v = "top_5v";
constexpr const char* top_nhv = "top_nhv";
constexpr const char* top_fpga_vol = "top_fpga_vol";
constexpr const char* top_fpga_aux = "top_fpga_aux";
constexpr const char* bot_12v = "bot_12v";
constexpr const char* bot_5v = "bot_5v";
constexpr const char* bot_fpga_vccint = "bot_fpga_vccint";
constexpr const char* bot_fpga_vccaux = "bot_fpga_vccaux";
constexpr const char* cbus_curr = "cbus_curr";
constexpr const char* real_speed = "real_speed";
constexpr const char* stress_test = "stress_test";
constexpr const char* upload_error_rate = "upload_error_rate";
constexpr const char* download_error_rate = "download_error_rate";
}  // namespace helios
}  // namespace limit
}  // namespace lidar
}  // namespace robosense