﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ZHONG_KAI_RELAY_CONTROLLER_H
#define ZHONG_KAI_RELAY_CONTROLLER_H

#include "relay_controller_driver/relay_controller_interface.h"

#include <atomic>
#include <memory>

#include <QtCore/QByteArray>
#include <QtCore/QThread>

class QSerialPort;

namespace robosense
{
namespace lidar
{

enum class RelayControllerChannelsNum
{
  RELAY_CONTROLLER_CHANNELS_NUM_2  = 2,
  RELAY_CONTROLLER_CHANNELS_NUM_4  = 4,
  RELAY_CONTROLLER_CHANNELS_NUM_8  = 8,
  RELAY_CONTROLLER_CHANNELS_NUM_16 = 16
};

class ZhongKaiRelayController : public QThread, public RelayControllerInterface
{
  Q_OBJECT
public:
  ZhongKaiRelayController(const QString& _port_name,
                          RelayControllerChannelsNum _channels_num,
                          quint8 _address  = 1,
                          QObject* _parent = Q_NULLPTR);
  explicit ZhongKaiRelayController(ZhongKaiRelayController&&)      = delete;
  explicit ZhongKaiRelayController(const ZhongKaiRelayController&) = delete;
  ZhongKaiRelayController& operator=(ZhongKaiRelayController&&) = delete;
  ZhongKaiRelayController& operator=(const ZhongKaiRelayController&) = delete;
  ~ZhongKaiRelayController() override;

  bool connectController(std::string& _error_msg) override;
  bool turnOnChannel(const uint8_t _channel, std::string& _error_msg) override;  // channel start from 1
  bool turnOffChannel(const uint8_t _channel, std::string& _error_msg) override;
  bool turnOnAllChannel(std::string& _error_msg) override;
  bool turnOffAllChannel(std::string& _error_msg) override;
  bool turnOnSingleChannel(const uint8_t _channel, std::string& _error_msg) override;
  bool turnOffSingleChannel(const uint8_t _channel, std::string& _error_msg) override;
  std::string getSerialPortName() const override;

  // bool getChannelState(quint16& channel_state);

private:
  bool waitForConnect(int _msec = 200);
  void run() override;
  void connect();
  bool writeCMD(const QByteArray& _char_data);
  bool readReply(const QByteArray& _char_data);
  bool waitForWrite(const QByteArray& _char_data);
  std::string getErrorMsg();

  bool turnOnChannel(const quint8 _channel);
  bool turnOnSingleChannel(const quint8 _channel);
  bool turnOffChannel(const quint8 _channel);
  bool turnOffSingleChannel(const quint8 _channel);
  bool turnOnAllChannel();
  bool turnOffAllChannel();

private:
  QSerialPort* serial_port_;

  QString port_name_;
  quint8 address_;
  RelayControllerChannelsNum controller_channels_num_;
  quint16 channel_status_;

  bool is_connected_;  // is connecting to serial port and controller is online
  bool is_quitting_;   // if quit outside, stop the controller and quit thread
  std::atomic<bool> is_working_;
  bool current_cmd_result_;
  QByteArray current_cmd_;

  QString error_msg_;
};

}  // namespace lidar
}  // namespace robosense

#endif  // ZHONG_KAI_RELAY_CONTROLLER_H
