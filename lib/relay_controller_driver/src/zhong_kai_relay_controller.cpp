﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "zhong_kai_relay_controller.h"

#include <array>
#include <chrono>
#include <cstdint>
#include <iostream>

#include <QtCore/QByteArray>
#include <QtCore/QCoreApplication>
#include <QtCore/QEventLoop>
#include <QtCore/QObject>
#include <QtCore/QTimer>
#include <QtCore/QtCore>
#include <QtSerialPort/QSerialPort>
#include <QtSerialPort/QSerialPortInfo>
#include <thread>

namespace robosense
{
namespace lidar
{

static quint8 checkSum8(const QByteArray& _msg)
{
  quint8 sum = 0;
  for (auto data : _msg)
  {
    sum += static_cast<quint8>(data);
  }
  return sum;
}

ZhongKaiRelayController::ZhongKaiRelayController(const QString& _port_name,
                                                 RelayControllerChannelsNum _channels_num,
                                                 quint8 _address,
                                                 QObject* _parent) :
  QThread(_parent),
  serial_port_(Q_NULLPTR),
  port_name_(_port_name),
  address_(_address),
  controller_channels_num_(_channels_num),
  channel_status_(0x0000),
  is_connected_(false),
  is_quitting_(false),
  is_working_(false),
  current_cmd_result_(true),
  current_cmd_("")
{
  if (_port_name.isEmpty())
  {
    error_msg_ = "serial port name is empty";
    return;
  }
  start();
}

ZhongKaiRelayController::~ZhongKaiRelayController()
{
  is_quitting_ = true;
  wait();
  if (serial_port_ == Q_NULLPTR)
  {
    return;
  }
  serial_port_->clear();
  serial_port_->close();
  serial_port_->deleteLater();
  serial_port_ = Q_NULLPTR;
}

bool ZhongKaiRelayController::connectController(std::string& _error_msg)
{
  bool result = waitForConnect();
  _error_msg  = getErrorMsg();
  return result;
}

bool ZhongKaiRelayController::turnOnChannel(const uint8_t _channel, std::string& _error_msg)
{
  bool result = turnOnChannel(_channel);
  _error_msg  = getErrorMsg();
  return result;
}

bool ZhongKaiRelayController::turnOffChannel(const uint8_t _channel, std::string& _error_msg)
{
  bool result = turnOffChannel(_channel);
  _error_msg  = getErrorMsg();
  return result;
}

bool ZhongKaiRelayController::turnOnAllChannel(std::string& _error_msg)
{
  bool result = turnOnAllChannel();
  _error_msg  = getErrorMsg();
  return result;
}

bool ZhongKaiRelayController::turnOffAllChannel(std::string& _error_msg)
{
  bool result = turnOffAllChannel();
  _error_msg  = getErrorMsg();
  return result;
}

bool ZhongKaiRelayController::turnOnSingleChannel(const uint8_t _channel, std::string& _error_msg)
{
  bool result = turnOffAllChannel(_error_msg);
  std::this_thread::sleep_for(std::chrono::microseconds(200));
  if (result)
  {
    result = turnOnChannel(_channel, _error_msg);
  }
  return result;
}

bool ZhongKaiRelayController::turnOffSingleChannel(const uint8_t _channel, std::string& _error_msg)
{
  bool result = turnOnAllChannel(_error_msg);
  std::this_thread::sleep_for(std::chrono::microseconds(200));
  if (result)
  {
    result = turnOffChannel(_channel, _error_msg);
  }
  return result;
}

bool ZhongKaiRelayController::turnOnChannel(const quint8 _channel)
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }

  if (_channel > static_cast<quint8>(controller_channels_num_))
  {
    error_msg_ = QString("channel input is larger than controller's total channels: ") +
                 QString::number(static_cast<quint8>(controller_channels_num_));
    return false;
  }

  std::array<char, 7> char_data = { 0x33, static_cast<char>(address_), 0x12, 0x00, 0x00,
                                    0x00, static_cast<char>(_channel) };
  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ |= static_cast<quint16>(static_cast<quint16>(0x0001U) << static_cast<quint8>(_channel - 1U));
    return true;
  }
  return false;
}

bool ZhongKaiRelayController::turnOnSingleChannel(const quint8 _channel)
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }

  if (_channel > static_cast<quint8>(controller_channels_num_))
  {
    error_msg_ = QString("channel input is larger than controller's total channels: ") +
                 QString::number(static_cast<quint8>(controller_channels_num_));
    return false;
  }
  uint16_t state_code           = ~(0x1U << _channel);
  std::array<char, 7> char_data = { 0x33,
                                    static_cast<char>(address_),
                                    0x15,
                                    static_cast<char>(state_code / 0x100),
                                    static_cast<char>(state_code % 0x100),
                                    0x00,
                                    static_cast<char>(controller_channels_num_) };
  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ |= static_cast<quint16>(static_cast<quint16>(0x0001U) << static_cast<quint8>(_channel - 1U));
    return true;
  }
  return false;
}

bool ZhongKaiRelayController::turnOffChannel(const quint8 _channel)
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }

  if (_channel > static_cast<quint8>(controller_channels_num_))
  {
    error_msg_ = QString("channel input is larger than controller's total channels: ") +
                 QString::number(static_cast<quint8>(controller_channels_num_));
    return false;
  }

  std::array<char, 7> char_data = { 0x33, static_cast<char>(address_), 0x11, 0x00, 0x00,
                                    0x00, static_cast<char>(_channel) };

  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ &=
      static_cast<quint16>(~static_cast<quint16>(static_cast<quint16>(0x0001U) << static_cast<quint8>(_channel - 1U)));
    return true;
  }
  return false;
}

bool ZhongKaiRelayController::turnOffSingleChannel(const quint8 _channel)
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }

  if (_channel > static_cast<quint8>(controller_channels_num_))
  {
    error_msg_ = QString("channel input is larger than controller's total channels: ") +
                 QString::number(static_cast<quint8>(controller_channels_num_));
    return false;
  }
  uint16_t state_code           = 0x1U << _channel;
  std::array<char, 7> char_data = { 0x33,
                                    static_cast<char>(address_),
                                    0x16,
                                    static_cast<char>(state_code / 0x100),
                                    static_cast<char>(state_code % 0x100),
                                    0x00,
                                    static_cast<char>(controller_channels_num_) };
  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ |= static_cast<quint16>(static_cast<quint16>(0x0001U) << static_cast<quint8>(_channel - 1U));
    return true;
  }
  return false;
}

bool ZhongKaiRelayController::turnOnAllChannel()
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }
  std::array<char, 7> char_data = { 0x33, static_cast<char>(address_), 0x14, 0x00, 0x00, 0x00, 0x00 };

  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ = static_cast<quint16>(0xFFFF);
    return true;
  }
  return false;
}

bool ZhongKaiRelayController::turnOffAllChannel()
{
  if (serial_port_ == Q_NULLPTR)
  {
    error_msg_ = "relay controller is not connected";
    return false;
  }
  std::array<char, 7> char_data = { 0x33, static_cast<char>(address_), 0x13, 0x00, 0x00, 0x00, 0x00 };

  if (waitForWrite(QByteArray(char_data.data(), 7)))
  {
    channel_status_ = static_cast<quint16>(0x0000);
    return true;
  }
  return false;
}

// NOTE 0x17 will return one channel by one channel, if 16 channels, this will be too many, so driver will keep a channel_status_
// bool RelayController::getChannelState(quint16& channel_state)
// {
//   if (serial_port_ == Q_NULLPTR)
//   {
//     error_msg_ = "relay controller is not connected";
//     return false;
//   }
//   char char_data[] = { 0x33, static_cast<char>(address_), 0x17, 0x00, 0x00, 0x00, 0x00 };

//   if (waitForWrite(char_data))
//   {
//     channel_state = channel_status_;
//     return true;
//   }
//   return false;
// }

void ZhongKaiRelayController::run()
{
  connect();
  while (!is_quitting_ && is_connected_)
  {
    if (!is_working_)
    {
      std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
    else
    {
      current_cmd_result_ = true;
      if (!writeCMD(current_cmd_))
      {
        error_msg_          = "write error";
        current_cmd_result_ = false;
      }
      if (current_cmd_result_ && !readReply(current_cmd_))
      {
        current_cmd_result_ = false;
      }
      is_working_ = false;
    }
  }
}

bool ZhongKaiRelayController::waitForWrite(const QByteArray& _char_data)
{
  while (current_cmd_ != nullptr)
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
  current_cmd_ = _char_data;
  is_working_  = true;
  while (is_working_)
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
  bool result = current_cmd_result_;
  current_cmd_.clear();
  return result;
}

bool ZhongKaiRelayController::writeCMD(const QByteArray& _char_data)
{
  serial_port_->clearError();
  serial_port_->clear();
  QByteArray send_data = _char_data;
  char check_sum       = static_cast<char>(checkSum8(send_data));
  send_data.append(check_sum);
  // qDebug() << send_data.toHex();
  serial_port_->write(send_data, 8);
  // missing the below line
  // it will not send when there is no event loop
  // and QApplication app->exec() does not work too
  return serial_port_->waitForBytesWritten(50);
}

bool ZhongKaiRelayController::readReply(const QByteArray& _char_data)
{
  QByteArray read_buf;
  int data_num = 0;
  while (data_num++ <= 8 && read_buf.size() < 8)
  {
    serial_port_->waitForReadyRead(100);
    read_buf += serial_port_->readAll();
  }
  // qDebug() << read_buf.toHex();
  if (read_buf.size() == 0)
  {
    error_msg_ = "read error";
    return false;
  }
  if (read_buf.size() != 8 || (read_buf[0] != static_cast<char>(0x22) && read_buf[0] != static_cast<char>(0x33)))
  {
    error_msg_ = "read data error1";
    return false;
  }
  if (checkSum8(read_buf.left(7)) != static_cast<uint8_t>(read_buf[7]))
  {
    // NOTE data reply by controller's check sum will add 1 sometime, this is a bug
    if (std::abs(checkSum8(read_buf.left(7)) - read_buf[7]) > 2)
    {
      error_msg_ = "read data check sum fail";
      return false;
    }
    std::cout << "----check sum max than 2" << std::endl;  // TODO
  }

  for (int i = 2; i < 7; ++i)
  {
    if (read_buf[i] != _char_data[i])
    {
      error_msg_ = "read data error2";
      return false;
    }
  }

  return true;
}

void ZhongKaiRelayController::connect()
{
  QList<QSerialPortInfo> infos = QSerialPortInfo::availablePorts();
  if (infos.isEmpty())
  {
    is_connected_ = false;
    error_msg_    = QString("there is no port name: ") + port_name_;
    return;
  }
  std::cout << "connect port: " << port_name_.toStdString() << std::endl;
  serial_port_ = new QSerialPort();
  serial_port_->setPortName(port_name_);
  if (serial_port_->open(QIODevice::ReadWrite))
  {
    serial_port_->setBaudRate(QSerialPort::Baud9600);
    serial_port_->setDataBits(QSerialPort::Data8);
    serial_port_->setStopBits(QSerialPort::OneStop);
    serial_port_->setFlowControl(QSerialPort::NoFlowControl);
    serial_port_->setParity(QSerialPort::NoParity);
    serial_port_->clearError();
    serial_port_->clear();
  }
  if (!serial_port_->isOpen())
  {
    is_connected_ = false;
    error_msg_    = QString("could not open port: ") + port_name_;
    if (Q_NULLPTR != serial_port_)
    {
      delete serial_port_;
      serial_port_ = Q_NULLPTR;
    }
    return;
  }

  is_connected_ = true;
}

std::string ZhongKaiRelayController::getSerialPortName() const { return port_name_.toStdString(); }

bool ZhongKaiRelayController::waitForConnect(int _msec)
{
  std::this_thread::sleep_for(std::chrono::milliseconds(_msec));
  if (!is_connected_)
  {
    return false;
  }
  if (turnOffAllChannel())
  {
    return true;
  }
  serial_port_->clear();
  serial_port_->close();
  serial_port_->deleteLater();
  serial_port_ = Q_NULLPTR;
  error_msg_   = "can not communicate with controller";
  return false;
}

std::string ZhongKaiRelayController::getErrorMsg()
{
  std::string msg_temp = error_msg_.toStdString();
  error_msg_           = "";
  return msg_temp;
}

}  // namespace lidar
}  // namespace robosense
