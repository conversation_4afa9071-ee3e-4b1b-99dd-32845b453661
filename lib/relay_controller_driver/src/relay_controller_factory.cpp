﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "relay_controller_driver/relay_controller_factory.h"
#include "zhong_kai_relay_controller.h"

namespace robosense
{
namespace lidar
{
constexpr std::array<const char*, RelayControllerFactory::RelayControllerType::RELAY_CONTROLLER_TYPE_NUM>
  RelayControllerFactory::RELAY_CONTROLLER_TYPE_INFO;

RelayControllerInterface* RelayControllerFactory::createRelayController(
  RelayControllerType _type,
  std::initializer_list<std::string> _hardware_param)
{
  auto getSerialPortName = [&]() -> QString {  // NOLINT(readability-identifier-naming)
    if (_hardware_param.size() != 1)
    {
      return { "" };
    }
    return QString::fromStdString(*_hardware_param.begin());
  };

  switch (_type)
  {
  case RelayControllerFactory::ZHONG_KAI_CONTROLLER_2:
  {
    return new ZhongKaiRelayController(getSerialPortName(),
                                       RelayControllerChannelsNum::RELAY_CONTROLLER_CHANNELS_NUM_2);
    break;
  }
  case RelayControllerFactory::ZHONG_KAI_CONTROLLER_4:
  {
    return new ZhongKaiRelayController(getSerialPortName(),
                                       RelayControllerChannelsNum::RELAY_CONTROLLER_CHANNELS_NUM_4);
    break;
  }
  case RelayControllerFactory::ZHONG_KAI_CONTROLLER_8:
  {
    return new ZhongKaiRelayController(getSerialPortName(),
                                       RelayControllerChannelsNum::RELAY_CONTROLLER_CHANNELS_NUM_8);
    break;
  }
  case RelayControllerFactory::ZHONG_KAI_CONTROLLER_16:
  {
    return new ZhongKaiRelayController(getSerialPortName(),
                                       RelayControllerChannelsNum::RELAY_CONTROLLER_CHANNELS_NUM_16);
    break;
  }
  case RelayControllerFactory::RELAY_CONTROLLER_TYPE_NUM:
  {
    return nullptr;
    break;
  }
  }
  return nullptr;
}
}  // namespace lidar
}  // namespace robosense