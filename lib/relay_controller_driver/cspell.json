// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON><PERSON>",
    "AUTOSAR",
    "<PERSON>L<PERSON>",
    "chardet",
    "CICD",
    "combobox",
    "congsir",
    "DIF<PERSON>",
    "dspinbox",
    "dtags",
    "Eigen",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "gtest",
    "hhmmss",
    "hicpp",
    "Liang",
    "libqt",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "MAINWINDOW",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "MSOP",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "opencv",
    "OPENMP",
    "pcap",
    "Pixmap",
    "qmake",
    "QMESSAGE",
    "qobject",
    "qsetting",
    "qsettings",
    "qtserialport",
    "robosense",
    "rsdata",
    "rsfsc",
    "RS<PERSON>CL<PERSON>",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "Shen",
    "SHIYAN",
    "spdlog",
    "suteng",
    "tablewidget",
    "tabwidget",
    "udev",
    "udevadm",
    "utest",
    "widgetaction",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "ZHONG"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
