﻿# 可控继电器模块驱动

本工程用于驱动继电器控制器，考虑到后续可能会增加或者替换继电器型号，例如转成网口的，换成其他厂商等，所以使用了工厂模式进行隔离。

目前支持的产品有：

+ **中凯电子厂的继电器控制器**

![](doc/img/product.png)

# 1 安装依赖

## 1.1 QtSerialPort

这个官方说Qt5.1以后会自带，但Ubuntu系统里没这个，

可以通过apt安装：
```bash
sudo apt-get install libqt5serialport5-dev
```

或者[下载源码编译安装]](https://wiki.qt.io/Qt_Serial_Port):

```bash
git clone git://code.qt.io/qt/qtserialport.git
cd qtserialport
git checkout v5.12.8
mkdir build
cd build
qmake ../qtserialport.pro
make
sudo make install
```

通过上述安装，QtSerialPort就被安装到`/usr/include/x86_64-linux-gnu/qt5/QtSerialPort/`目录下了。


# 2. 基本框架

继电器控制器的通信协议请详见doc文件夹中相关通信协议文档


## 2.1 设计原理

使用工厂模式+可变参数实现对不同信号、不同硬件连接方式（网口、串口）的适配。

### 2.1.1 中凯电子厂的继电器控制器代码实现原理

使用QSerialPort来实现串口功能，由于`QSerialPort`只能在QThread内执行，所以构造函数中获取到串口号等信息后直接启动线程执行connect。串口的发送和接收都是在新线程中执行的，`turnOnChannel`、`turnOffChannel`这些功能函数只起到传递命令并等待的作用。

为了保证控制器已经接收到了信号，在发送一个命令后，会等待接收反馈信息并分析是否满足协议，当且仅当发送成功且接收成功才认为当次控制成功。

由于0x17返回继电器状态的功能，会每个通道逐个返回，如果16个通道的会返回16串信息。。。数量太多，解析复杂，所以就在程序中有个`channel_status_`的变量来记录当前状态。


## 2.2 使用方法

具体使用方法请参考`example.cpp`的示例以及`relay_controller_interface.h`中对各个函数的注释。

对本工程的**编译**，可以在当前目录下新建一个`build`目录，然后在`build`目录下运行`cmake ../`和`make`

## 2.3 添加到你的工程

1. 使用git submodule add xxx.git(本工程git地址) lib将代码以子模块形式放到lib目录
2. 在你的主CMakeLists.txt中加入`add_subdirectory(lib/relay_controller_driver)`
3. 在CMakeLists.txt中需要链接的target中，`target_link_libraries(your_object relay_controller_driver_shared)`，你也可以选择链接静态库`target_link_libraries(your_object relay_controller_driver_static)`。链接动态库时记得install的时候也加上动态库
4. 在你的CPP中`#include "relay_controller_driver/relay_controller_factory.h"`即可使用相关功能
5. 如果选择的是动态库，在install步骤记得加上relay_controller_driver_shared target：
   ```cmake
   install(
     TARGETS ${EXAMPLE_NAME} relay_controller_driver_shared
     PERMISSIONS
       OWNER_EXECUTE
       OWNER_WRITE
       OWNER_READ
       GROUP_EXECUTE
       GROUP_READ
       WORLD_EXECUTE
       WORLD_READ
     ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
     LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
     RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME})
   ```

# 3. Q&A

## Q1 如果我想一台电脑同时连多个控制器怎么办？

**A:**
驱动中直接new多个对象是没有问题的，但如果电脑中存在多个串口设备，他们的启动顺序是不固定的。也就是说，今天可能A设备是ttyUSB0，明天A可能就是ttyUSB1了。这时候可以通过插入的USB口在内核中的编号来进行区分，也就要求插入的口不能变动。设置方式是：

使用rsfsc_lib中的串口设置脚本