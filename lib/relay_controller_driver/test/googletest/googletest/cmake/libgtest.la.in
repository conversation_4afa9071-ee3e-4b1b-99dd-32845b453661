# libgtest.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6

# Please DO NOT delete this file!
# It is necessary for linking the library.

# Names of this library.
library_names='libgtest.so'

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='@CMAKE_INSTALL_FULL_LIBDIR@'
