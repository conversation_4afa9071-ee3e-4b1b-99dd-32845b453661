# GoogleTest User's Guide

## Welcome to GoogleTest!

GoogleTest is Google's C++ testing and mocking framework. This user's guide has
the following contents:

*   [GoogleTest Primer](primer.md) - Teaches you how to write simple tests using
    GoogleTest. Read this first if you are new to GoogleTest.
*   [GoogleTest Advanced](advanced.md) - Read this when you've finished the
    Primer and want to utilize GoogleTest to its full potential.
*   [GoogleTest Samples](samples.md) - Describes some GoogleTest samples.
*   [GoogleTest FAQ](faq.md) - Have a question? Want some tips? Check here
    first.
*   [Mocking for Dummies](gmock_for_dummies.md) - Teaches you how to create mock
    objects and use them in tests.
*   [Mocking Cookbook](gmock_cook_book.md) - Includes tips and approaches to
    common mocking use cases.
*   [Mocking Cheat Sheet](gmock_cheat_sheet.md) - A handy reference for
    matchers, actions, invariants, and more.
*   [Mocking FAQ](gmock_faq.md) - Contains answers to some mocking-specific
    questions.
