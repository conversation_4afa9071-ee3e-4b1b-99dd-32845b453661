﻿cmake_minimum_required(VERSION 3.5.0)
cmake_policy(SET CMP0048 NEW)

project(relay_controller_driver VERSION 1.0.0)
# add_compile_options(-std=c++11)

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
  set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
endif()

set(CMAKE_BUILD_TYPE RelWithDebInfo)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
option(BUILD_RELAY_CONTROLLER_DRIVER_TEST "build gtest or not" OFF)
option(BUILD_RELAY_CONTROLLER_DRIVER_EXAMPLE "build example or not" OFF)

find_package(Git QUIET)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

find_package(
  Qt5
  COMPONENTS SerialPort
  REQUIRED)

# =========================
# Create Object Library
# =========================
qt5_wrap_cpp(MOC_ROTATOR_HEAD src/zhong_kai_relay_controller.h)
add_library(${PROJECT_NAME}_object OBJECT ${MOC_ROTATOR_HEAD} src/zhong_kai_relay_controller.cpp
                                          src/relay_controller_factory.cpp)

target_include_directories(${PROJECT_NAME}_object PUBLIC include)
target_include_directories(${PROJECT_NAME}_object SYSTEM PRIVATE ${Qt5SerialPort_INCLUDE_DIRS}
                                                                 ${CMAKE_CURRENT_BINARY_DIR})

set_target_properties(
  ${PROJECT_NAME}_object
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)
if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_compile_options(${PROJECT_NAME}_object PUBLIC /O2 /utf-8)
else()
  target_compile_options(${PROJECT_NAME}_object PUBLIC -fPIC -Wall -O3 -g1)
endif()
target_compile_definitions(${PROJECT_NAME}_object PUBLIC QT_NO_KEYWORDS QT_NO_DEBUG_OUTPUT)

# =========================
# Create Shared Library
# =========================
# set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
add_library(relay_controller_driver_shared SHARED $<TARGET_OBJECTS:${PROJECT_NAME}_object>)
target_include_directories(relay_controller_driver_shared PUBLIC include)
set_target_properties(relay_controller_driver_shared PROPERTIES VERSION ${PROJECT_VERSION} OUTPUT_NAME
                                                                                           relay_controller_driver)
target_link_libraries(relay_controller_driver_shared Qt5::SerialPort)

# =========================
# Create Static Library
# =========================
# set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
add_library(relay_controller_driver_static STATIC $<TARGET_OBJECTS:${PROJECT_NAME}_object>)
target_include_directories(relay_controller_driver_static PUBLIC include)
set_target_properties(relay_controller_driver_static PROPERTIES VERSION ${PROJECT_VERSION} OUTPUT_NAME
                                                                                           relay_controller_driver)
target_link_libraries(relay_controller_driver_static Qt5::SerialPort)

# =========================
# Create Example
# =========================
if(BUILD_RELAY_CONTROLLER_DRIVER_EXAMPLE)
  add_subdirectory(example)
endif(BUILD_RELAY_CONTROLLER_DRIVER_EXAMPLE)

# =========================
# Create Unit Test
# =========================
if(BUILD_RELAY_CONTROLLER_DRIVER_TEST)
  add_subdirectory(test)
endif(BUILD_RELAY_CONTROLLER_DRIVER_TEST)
