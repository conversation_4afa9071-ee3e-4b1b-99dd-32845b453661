
# refer to https://docs.gitlab.com/ee/ci/variables/predefined_variables.html
# refer to https://docs.gitlab.com/ee/ci/variables/index.html#list-all-environment-variables
# refer to https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section
# refer to https://docs.gitlab.com/ee/ci/runners/configure_runners.html#configure-runner-behavior-with-variables
image:
  name: "***********:5000/rs_ubuntu:rsfsc_noetic"

stages:
  - build
  - analyze
  - utest

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_STRATEGY: clone
  # GITLAB_USER_EMAIL: <EMAIL>
  # GITLAB_USER_NAME: HaoQChen
  # git clone path should be in ci builds dir and runner should be at least v11.10
  GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_PROJECT_NAME
  # CI_BUILDS_DIR: /home/<USER>/ this should be set in config.toml
  # CI_PROJECT_DIR: /opt

before_script:
  - git config --global --add safe.directory '*' 

build:
  stage: build
  tags:
    - ubuntu4common_driver
  script: mkdir build && cd build && cmake ../ -DBUILD_RELAY_CONTROLLER_DRIVER_TEST=ON -DBUILD_RELAY_CONTROLLER_DRIVER_EXAMPLE=ON && make
  artifacts:
    expire_in: 20 mins
    paths:
      - build/*
  retry: 2
  # artifacts:
  #   paths:
  #     - release/

check-format:
  stage: analyze
  script: .githooks/pre-commit 1
  tags:
    - ubuntu4common_driver
  retry: 2

check-clang-tidy:
  stage: analyze
  script: .githooks/pre-commit 3
  tags:
    - ubuntu4common_driver
  retry: 2

check-identifier-naming:
  stage: analyze
  script: .githooks/pre-commit 2
  tags:
    - ubuntu4common_driver
  retry: 2

check-code-spell:
  stage: analyze
  allow_failure: true
  script: .githooks/pre-commit 4
  tags:
    - ubuntu4common_driver
  retry: 2

check-rules-file-latest:
  stage: analyze
  allow_failure: true
  script: .githooks/pre-commit 5
  tags:
    - ubuntu4common_driver
  retry: 2

google-test:
  stage: utest
  script: cd build && ./test/test_main
  tags:
    - ubuntu4common_driver
  retry: 2
