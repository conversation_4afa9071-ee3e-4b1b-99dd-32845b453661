﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RELAY_CONTROLLER_INTERFACE_H
#define RELAY_CONTROLLER_INTERFACE_H
// #include <cstdint>
#include <string>
namespace robosense
{
namespace lidar
{
class RelayControllerInterface
{
public:
  RelayControllerInterface()                                         = default;
  explicit RelayControllerInterface(RelayControllerInterface&&)      = delete;
  explicit RelayControllerInterface(const RelayControllerInterface&) = delete;
  RelayControllerInterface& operator=(RelayControllerInterface&&) = delete;
  RelayControllerInterface& operator=(const RelayControllerInterface&) = delete;
  virtual ~RelayControllerInterface()                                  = default;
  /**
   * @brief connect relay controller, use RS232
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if connect relay controller successfully, otherwise return false
   */
  virtual bool connectController(std::string& _error_msg) = 0;
  /**
   * @brief turn on relay channel
   * @param _channel the index of relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if turn on relay channel successfully, otherwise return false
   */
  virtual bool turnOnChannel(const uint8_t _channel, std::string& _error_msg) = 0;
  /**
   * @brief turn off relay channel
   * @param _channel the index of relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if turn off relay channel successfully, otherwise return false
   */
  virtual bool turnOffChannel(const uint8_t _channel, std::string& _error_msg) = 0;
  /**
   * @brief turn on all relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if turn on all relay channel successfully, otherwise return false
   */
  virtual bool turnOnAllChannel(std::string& _error_msg) = 0;
  /**
   * @brief turn off all relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if turn off all relay channel successfully, otherwise return false
   */
  virtual bool turnOffAllChannel(std::string& _error_msg) = 0;
  /**
   * @brief turn on single relay channel and the other channels will turn off
   * @param _channel the index of relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if execute successfully, otherwise return false
   */
  virtual bool turnOnSingleChannel(const uint8_t _channel, std::string& _error_msg) = 0;
  /**
   * @brief turn off single relay channel and the other channels will turn on
   * @param _channel the index of relay channel
   * @param _error_msg if return false, it will show error message
   * 
   * @return true if execute successfully, otherwise return false
   */
  virtual bool turnOffSingleChannel(const uint8_t _channel, std::string& _error_msg) = 0;
  /**
   * @brief to get serial port name
   * 
   * @return serial port name(std::string)
   */
  virtual std::string getSerialPortName() const = 0;
};
}  // namespace lidar
}  // namespace robosense
#endif  //RELAY_CONTROLLER_INTERFACE_H