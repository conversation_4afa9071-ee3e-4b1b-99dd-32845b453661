﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/*
 * @file   relay_controller_factory.h
 * <AUTHOR> Chen (<EMAIL>)
 * @brief     relay controller driver factory
 * @version 1.0.0
 * @date 2022-12-22
 * 
 * You should notify Antoine when you change this file, so we can all share your optimization
 * 
**/
#ifndef RELAY_CONTROLLER_FACTORY_H
#define RELAY_CONTROLLER_FACTORY_H
#include "relay_controller_driver/relay_controller_interface.h"

#include <array>
#include <initializer_list>
#include <string>

namespace robosense
{
namespace lidar
{

class RelayControllerFactory
{
public:
  enum RelayControllerType
  {
    ZHONG_KAI_CONTROLLER_2 = 0,
    ZHONG_KAI_CONTROLLER_4,
    ZHONG_KAI_CONTROLLER_8,
    ZHONG_KAI_CONTROLLER_16,
    RELAY_CONTROLLER_TYPE_NUM
  };
  static constexpr std::array<const char*, RELAY_CONTROLLER_TYPE_NUM> RELAY_CONTROLLER_TYPE_INFO = {
    "中凯二路继电器", "中凯四路继电器", "中凯八路继电器", "中凯十六路继电器"
  };
  static RelayControllerInterface* createRelayController(RelayControllerType _type,
                                                         std::initializer_list<std::string> _hardware_param);
};
}  // namespace lidar
}  // namespace robosense
#endif