﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include <iostream>
#include <memory>
#include <thread>

#include "relay_controller_driver/relay_controller_factory.h"

int main(int /*argc*/, char** /*argv*/)
{
  // 本示例只适用于4路中凯继电器
  std::unique_ptr<robosense::lidar::RelayControllerInterface> ptr_relay_control;

  ptr_relay_control.reset(robosense::lidar::RelayControllerFactory::createRelayController(
    robosense::lidar::RelayControllerFactory::ZHONG_KAI_CONTROLLER_4, { std::string("ttyUSB1") }));

  std::string error_msg;
  if (!ptr_relay_control->connectController(error_msg))
  {
    std::cout << "Failed to connect: " << ptr_relay_control->getSerialPortName() << " with error msg: " << error_msg
              << std::endl;
  }
  else
  {
    std::cout << "connect successfully" << std::endl;
  }

  for (int i = 1; i <= 4; i++)
  {
    if (!ptr_relay_control->turnOnChannel(i, error_msg))
    {
      std::cout << "Failed to turn on channel " << i << ", error msg: " << error_msg << std::endl;
    }
    else
    {
      std::cout << "turn on channel " << i << " successfully" << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
    if (!ptr_relay_control->turnOffChannel(i, error_msg))
    {
      std::cout << "Failed to turn off channel " << i << ", error msg: " << error_msg << std::endl;
    }
    else
    {
      std::cout << "turn off channel " << i << " successfully" << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }

  for (int i = 1; i <= 4; i++)
  {
    if (!ptr_relay_control->turnOnSingleChannel(i, error_msg))
    {
      std::cout << "Failed to turn on single channel " << i << ", error msg: " << error_msg << std::endl;
    }
    else
    {
      std::cout << "turn on single channel " << i << " successfully" << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
    if (!ptr_relay_control->turnOffSingleChannel(i, error_msg))
    {
      std::cout << "Failed to turn off channel " << i << ", error msg: " << error_msg << std::endl;
    }
    else
    {
      std::cout << "turn off channel " << i << " successfully" << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }

  if (!ptr_relay_control->turnOnAllChannel(error_msg))
  {
    std::cout << "Failed to turn on all channel, error msg: " << error_msg << std::endl;
  }
  else
  {
    std::cout << "turn on all channel successfully" << std::endl;
  }
  std::this_thread::sleep_for(std::chrono::seconds(2));

  if (ptr_relay_control != nullptr)
  {
    ptr_relay_control.reset();
  }

  return 0;
}