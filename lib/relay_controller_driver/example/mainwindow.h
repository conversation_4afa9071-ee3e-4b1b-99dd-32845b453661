﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RELAY_CONTROLLER_DRIVER_MAINWINDOW_H
#define RELAY_CONTROLLER_DRIVER_MAINWINDOW_H
#include "relay_controller_driver/relay_controller_interface.h"
#include <QtWidgets/QMainWindow>
#include <QtWidgets/qlineedit.h>
#include <QtWidgets/qpushbutton.h>
#include <array>
#include <memory>
#include <qobjectdefs.h>
namespace robosense
{
namespace lidar
{
class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(int _argc, char** _argv, QWidget* _parent = nullptr);
  ~MainWindow() override;

Q_SIGNALS:
  void signalTurnOnAllChannel();
  void signalTurnOffAllChannel();
  void signalCloseTemperatureCycling();

private Q_SLOTS:
  void slotConnectCom1();
  void slotConnectCom2();
  void slotConnectCom3();
  void slotConnectCom4();
  void slotTurnOnAllChannel();
  void slotTurnOffAllChannel();
  void slotOpenTemperatureCycling();

private:
  void initLayout();
  void readSetting();
  void writeSetting();
  void connectCom(int _current_index);
  void connectSignal();
  void enableAllWidget(bool _enable);

private:
  QLineEdit* lineedit_com1_name_;
  QLineEdit* lineedit_com2_name_;
  QLineEdit* lineedit_com3_name_;
  QLineEdit* lineedit_com4_name_;
  QPushButton* pushbutton_com1_connect_;
  QPushButton* pushbutton_com2_connect_;
  QPushButton* pushbutton_com3_connect_;
  QPushButton* pushbutton_com4_connect_;
  QPushButton* pushbutton_turn_on_all_channel_;
  QPushButton* pushbutton_turn_off_all_channel_;
  QLineEdit* lineedit_temperature_cycling_set_;
  QPushButton* pushbutton_open_temperature_cycling_;
  std::array<std::shared_ptr<RelayControllerInterface>, 4> controllers_;
  std::array<bool, 4> connect_status_;
  bool is_stop_temperature_cycling_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //RELAY_CONTROLLER_DRIVER_MAINWINDOW_H