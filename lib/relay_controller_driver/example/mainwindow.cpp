﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mainwindow.h"
#include "relay_controller_driver/relay_controller_factory.h"
#include <QtWidgets/qgridlayout.h>
#include <QtWidgets/qlabel.h>
#include <QtWidgets/qlineedit.h>
#include <QtWidgets/qmainwindow.h>
#include <QtWidgets/qmessagebox.h>
#include <QtWidgets/qpushbutton.h>
#include <QtWidgets/qwidget.h>
#include <bits/types/time_t.h>
#include <chrono>
#include <cmath>
#include <iostream>
#include <ostream>
#include <qicon.h>
#include <qnamespace.h>
#include <qsettings.h>
#include <qthread.h>
#include <thread>
#include <vector>
namespace robosense
{
namespace lidar
{
MainWindow::MainWindow(int /*_argc*/, char** /*_argv*/, QWidget* _parent) :
  QMainWindow(_parent),
  lineedit_com1_name_(new QLineEdit(this)),
  lineedit_com2_name_(new QLineEdit(this)),
  lineedit_com3_name_(new QLineEdit(this)),
  lineedit_com4_name_(new QLineEdit(this)),
  pushbutton_com1_connect_(new QPushButton(u8"连接", this)),
  pushbutton_com2_connect_(new QPushButton(u8"连接", this)),
  pushbutton_com3_connect_(new QPushButton(u8"连接", this)),
  pushbutton_com4_connect_(new QPushButton(u8"连接", this)),
  pushbutton_turn_on_all_channel_(new QPushButton(u8"打开所有通道", this)),
  pushbutton_turn_off_all_channel_(new QPushButton(u8"关闭所有通道", this)),
  lineedit_temperature_cycling_set_(new QLineEdit(this)),
  pushbutton_open_temperature_cycling_(new QPushButton(u8"打开温循", this)),
  controllers_({ nullptr, nullptr, nullptr, nullptr }),
  connect_status_({ false, false, false, false }),
  is_stop_temperature_cycling_(false)
{
  setWindowTitle(u8"继电器控制示例");
  setWindowIcon(QIcon(":/img/icon.png"));
  initLayout();
  readSetting();
  connectSignal();
}

MainWindow::~MainWindow() { writeSetting(); }

void MainWindow::slotConnectCom1()
{
  int current_index = 0;
  connectCom(current_index);
}
void MainWindow::slotConnectCom2()
{
  int current_index = 1;
  connectCom(current_index);
}
void MainWindow::slotConnectCom3()
{
  int current_index = 2;
  connectCom(current_index);
}
void MainWindow::slotConnectCom4()
{
  int current_index = 3;
  connectCom(current_index);
}
void MainWindow::slotTurnOnAllChannel()
{
  std::string error_msg;
  for (int i = 0; i < 4; i++)
  {
    if (connect_status_.at(i))
    {
      if (!controllers_.at(i)->turnOnAllChannel(error_msg))
      {
        std::cout << u8"继电器" << i + 1 << u8"打开所有通道失败，请检查串口连接并重启软件" << std::endl;
      }
    }
    else
    {
      std::cout << u8"继电器" << i + 1 << u8"未连接" << std::endl;
    }
  }
}
void MainWindow::slotTurnOffAllChannel()
{
  std::string error_msg;
  for (int i = 0; i < 4; i++)
  {
    if (connect_status_.at(i))
    {
      if (!controllers_.at(i)->turnOffAllChannel(error_msg))
      {
        std::cout << u8"继电器" << i + 1 << u8"关闭所有通道失败，请检查串口连接并重启软件" << std::endl;
      }
    }
    else
    {
      std::cout << u8"继电器" << i + 1 << u8"未连接" << std::endl;
    }
  }
}

void MainWindow::slotOpenTemperatureCycling()
{
  if (pushbutton_open_temperature_cycling_->text() == "打开温循")
  {
    // 获取当前连接状态，未完全连接退出
    bool is_all_connected = true;
    for (const auto& status : connect_status_)
    {
      if (!status)
      {
        is_all_connected = false;
        break;
      }
    }
    if (!is_all_connected)
    {
      QMessageBox::information(this, "warning", u8"请先连接全部继电器!");
      return;
    }
    // 获取当前设置，设置格式异常退出
    QString set = lineedit_temperature_cycling_set_->text();
    if (set.isEmpty())
    {
      QMessageBox::information(this, "warning", u8"设置为空，请检查格式!");
      return;
    }
    QStringList set_string_list = set.split(',');
    std::vector<int> set_list;
    bool* trans_ok  = new bool(true);
    int current_set = 0;
    for (const auto& value : set_string_list)
    {
      current_set = value.toInt(trans_ok);
      if (!*trans_ok)
      {
        QMessageBox::information(this, "warning", u8"设置格式异常，请检查格式!");
        return;
      }
      set_list.emplace_back(current_set);
    }
    // 更新UI
    pushbutton_open_temperature_cycling_->setText(u8"关闭温循");
    is_stop_temperature_cycling_ = false;
    enableAllWidget(false);
    // 开启温度循环线程
    QThread* thread = QThread::create([=]() {
      int current_index = 0;
      time_t first_time = std::chrono::system_clock::to_time_t(
        std::chrono::time_point_cast<std::chrono::microseconds>(std::chrono::system_clock::now()));
      if (set_list.at(current_index) > 0)
      {
        signalTurnOnAllChannel();
      }
      else
      {
        signalTurnOffAllChannel();
      }
      while (!is_stop_temperature_cycling_ && current_index < set_list.size())
      {
        if ((std::chrono::system_clock::to_time_t(
               std::chrono::time_point_cast<std::chrono::microseconds>(std::chrono::system_clock::now())) -
             first_time) >= std::abs(set_list.at(current_index) * 60))
        {
          current_index++;
          if (current_index >= set_list.size())
          {
            break;
          }
          first_time = std::chrono::system_clock::to_time_t(
            std::chrono::time_point_cast<std::chrono::microseconds>(std::chrono::system_clock::now()));
          if (set_list.at(current_index) > 0)
          {
            signalTurnOnAllChannel();
          }
          else
          {
            signalTurnOffAllChannel();
          }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
      signalTurnOffAllChannel();
      if (!is_stop_temperature_cycling_)
      {
        signalCloseTemperatureCycling();
      }
    });
    QObject::connect(thread, &QThread::finished, thread, &QThread::deleteLater);
    thread->start();
  }
  else
  {
    pushbutton_open_temperature_cycling_->setText(u8"打开温循");
    is_stop_temperature_cycling_ = true;
    enableAllWidget(true);
  }
}

void MainWindow::initLayout()
{
  QWidget* widget_main     = new QWidget(this);
  QGridLayout* layout_main = new QGridLayout();
  int row                  = 0;
  layout_main->addWidget(new QLabel(u8"继电器控制界面", this), row, 0, 1, 3);
  row++;
  layout_main->addWidget(new QLabel(u8"继电器1串口号：", this), row, 0);
  layout_main->addWidget(lineedit_com1_name_, row, 1);
  layout_main->addWidget(pushbutton_com1_connect_, row, 2);
  row++;
  layout_main->addWidget(new QLabel(u8"继电器2串口号：", this), row, 0);
  layout_main->addWidget(lineedit_com2_name_, row, 1);
  layout_main->addWidget(pushbutton_com2_connect_, row, 2);
  row++;
  layout_main->addWidget(new QLabel(u8"继电器3串口号：", this), row, 0);
  layout_main->addWidget(lineedit_com3_name_, row, 1);
  layout_main->addWidget(pushbutton_com3_connect_, row, 2);
  row++;
  layout_main->addWidget(new QLabel(u8"继电器4串口号：", this), row, 0);
  layout_main->addWidget(lineedit_com4_name_, row, 1);
  layout_main->addWidget(pushbutton_com4_connect_, row, 2);
  row++;
  layout_main->addWidget(new QLabel(u8"继电器操作：", this), row, 0);
  layout_main->addWidget(pushbutton_turn_on_all_channel_, row, 1);
  layout_main->addWidget(pushbutton_turn_off_all_channel_, row, 2);
  row++;
  layout_main->addWidget(new QLabel(u8"温循操作："), row, 0, 2, 1);
  layout_main->addWidget(new QLabel(u8"格式示例：+50,-70,+60,-70,+30"), row, 1, 1, 2);
  row++;
  layout_main->addWidget(lineedit_temperature_cycling_set_, row, 1);
  layout_main->addWidget(pushbutton_open_temperature_cycling_, row, 2);
  widget_main->setLayout(layout_main);
  this->setCentralWidget(widget_main);
}

void MainWindow::readSetting()
{
  QSettings settings("RoboSense", "relay_controller_driver_demo");
  lineedit_com1_name_->setText(settings.value("com1_name", "").toString());
  lineedit_com2_name_->setText(settings.value("com2_name", "").toString());
  lineedit_com3_name_->setText(settings.value("com3_name", "").toString());
  lineedit_com4_name_->setText(settings.value("com4_name", "").toString());
}

void MainWindow::writeSetting()
{
  QSettings settings("RoboSense", "relay_controller_driver_demo");
  settings.setValue("com1_name", lineedit_com1_name_->text());
  settings.setValue("com2_name", lineedit_com2_name_->text());
  settings.setValue("com3_name", lineedit_com3_name_->text());
  settings.setValue("com4_name", lineedit_com4_name_->text());
}

void MainWindow::connectCom(int _current_index)
{
  if (connect_status_.at(_current_index))
  {
    std::cout << u8"当前继电器已连接" << std::endl;
    return;
  }
  QString com_name;
  switch (_current_index)
  {
  case 0:
  {
    com_name = lineedit_com1_name_->text();
    break;
  }
  case 1:
  {
    com_name = lineedit_com2_name_->text();
    break;
  }
  case 2:
  {
    com_name = lineedit_com3_name_->text();
    break;
  }
  case 3:
  {
    com_name = lineedit_com4_name_->text();
    break;
  }
  default: break;
  }
  if (com_name.isEmpty())
  {
    std::cout << u8"当前继电器串口名为空" << std::endl;
    return;
  }
  controllers_.at(_current_index)
    .reset(RelayControllerFactory::createRelayController(
      robosense::lidar::RelayControllerFactory::ZHONG_KAI_CONTROLLER_16, { com_name.toStdString() }));
  std::string error_msg;
  if (!controllers_.at(_current_index)->connectController(error_msg))
  {
    std::cout << "Failed to connect: " << controllers_.at(_current_index)->getSerialPortName()
              << " with error msg: " << error_msg << std::endl;
    return;
  }
  connect_status_.at(_current_index) = true;
  switch (_current_index)
  {
  case 0:
  {
    pushbutton_com1_connect_->setEnabled(false);
    break;
  }
  case 1:
  {
    pushbutton_com2_connect_->setEnabled(false);
    break;
  }
  case 2:
  {
    pushbutton_com3_connect_->setEnabled(false);
    break;
  }
  case 3:
  {
    pushbutton_com4_connect_->setEnabled(false);
    break;
  }
  default: break;
  }
}

void MainWindow::connectSignal()
{
  connect(pushbutton_com1_connect_, &QPushButton::clicked, this, &MainWindow::slotConnectCom1);
  connect(pushbutton_com2_connect_, &QPushButton::clicked, this, &MainWindow::slotConnectCom2);
  connect(pushbutton_com3_connect_, &QPushButton::clicked, this, &MainWindow::slotConnectCom3);
  connect(pushbutton_com4_connect_, &QPushButton::clicked, this, &MainWindow::slotConnectCom4);
  connect(pushbutton_turn_on_all_channel_, &QPushButton::clicked, this, &MainWindow::slotTurnOnAllChannel);
  connect(pushbutton_turn_off_all_channel_, &QPushButton::clicked, this, &MainWindow::slotTurnOffAllChannel);
  connect(this, &MainWindow::signalTurnOnAllChannel, this, &MainWindow::slotTurnOnAllChannel, Qt::QueuedConnection);
  connect(this, &MainWindow::signalTurnOffAllChannel, this, &MainWindow::slotTurnOffAllChannel, Qt::QueuedConnection);
  connect(pushbutton_open_temperature_cycling_, &QPushButton::clicked, this, &MainWindow::slotOpenTemperatureCycling);
  connect(this, &MainWindow::signalCloseTemperatureCycling, this, &MainWindow::slotOpenTemperatureCycling);
}

void MainWindow::enableAllWidget(bool _enable)
{
  lineedit_com1_name_->setEnabled(_enable);
  lineedit_com2_name_->setEnabled(_enable);
  lineedit_com3_name_->setEnabled(_enable);
  lineedit_com4_name_->setEnabled(_enable);
  pushbutton_turn_on_all_channel_->setEnabled(_enable);
  pushbutton_turn_off_all_channel_->setEnabled(_enable);
  lineedit_temperature_cycling_set_->setEnabled(_enable);
}
}  // namespace lidar
}  // namespace robosense