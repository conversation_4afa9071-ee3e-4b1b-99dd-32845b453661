﻿find_package(
  Qt5
  COMPONENTS SerialPort Widgets Core
  REQUIRED)

set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/relay_example ${CMAKE_INSTALL_PREFIX}/lib/relay_example/lib)

# 设置moc rcc uic
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)
qt5_add_resources(QT_RESOURCES_CPP resource/resource.qrc)

add_executable(relay_example)
target_sources(relay_example PRIVATE demo.cpp mainwindow.h mainwindow.cpp ${QT_RESOURCES_CPP})
target_include_directories(relay_example SYSTEM PRIVATE ${Qt5Widgets_INCLUDE_DIRS})
target_link_libraries(relay_example relay_controller_driver_shared Qt5::Core Qt5::Widgets)
set_target_properties(
  relay_example
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)

# delete old deb TODO check if it is run by cpack.
file(
  GLOB_RECURSE deb_files
  LIST_DIRECTORIES false
  RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/release
  ${PROJECT_NAME}*.deb)

foreach(deb_file IN LISTS deb_files)
  if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/release/${deb_file}")
    message("remove deb_file: " ${CMAKE_CURRENT_SOURCE_DIR}/release/${deb_file})
    file(REMOVE ${CMAKE_CURRENT_SOURCE_DIR}/release/${deb_file})
  endif()
endforeach()

install(
  TARGETS relay_example relay_controller_driver_shared
  PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ
  ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/relay_example
  LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/relay_example
  RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/relay_example)

install(
  DIRECTORY script/ resource/
  DESTINATION ${CMAKE_INSTALL_PREFIX}/share/relay_example
  USE_SOURCE_PERMISSIONS
  DIRECTORY_PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ)

install(FILES script/relay_example.desktop DESTINATION /usr/share/applications)

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_NAME relay_example)
set(CPACK_DEBIAN_PACKAGE_DEPENDS "libqt5serialport5, libqt5widgets5, libqt5gui5, libqt5core5a")
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "ChaosTang") # required
set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "amd64")
set(CPACK_PACKAGE_VERSION_MAJOR 1)
set(CPACK_PACKAGE_VERSION_MINOR 0)
set(CPACK_PACKAGE_VERSION_PATCH 0)
set(CPACK_PACKAGE_ICON resource/img/icon.png)
set(CPACK_OUTPUT_FILE_PREFIX "${CMAKE_CURRENT_SOURCE_DIR}/release")

set(CPACK_PACKAGE_FILE_NAME "relay_example_1_0_0")

include(CPack)
