﻿cmake_minimum_required(VERSION 3.14.0)

string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
project(mech_communication VERSION 2.3.0.${PROJECT_COMPILE_DATE})

cmake_policy(SET CMP0048 NEW)
if(WIN32)
  cmake_policy(SET CMP0074 NEW)
endif(WIN32)

option(BUILD_MECH_COMMUNICATION_EXAMPLE "build example or not" OFF)
option(BUILD_TEST "build gtest or not" OFF)

# =========================
# Set C++ Standard
# =========================
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
find_package(
  Qt5
  COMPONENTS Widgets
  REQUIRED)
include(cmake/FindRSFSCLog.cmake)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

add_subdirectory(src/widgets EXCLUDE_FROM_ALL)

# 判断是否存在rsfsc_comm_client的target
if(NOT TARGET rsfsc_comm_client)
  # 保存原来的值
  set(FETCHCONTENT_BASE_DIR_BAK ${FETCHCONTENT_BASE_DIR})

  # 设置自定义下载路径
  set(FETCHCONTENT_BASE_DIR "${CMAKE_SOURCE_DIR}/lib/dep")
  if(NOT DEFINED MECH_COMM_LIB_GIT_URL)
    execute_process(
      COMMAND git config --get remote.origin.url
      WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
      OUTPUT_VARIABLE CURRENT_GIT_URL
      OUTPUT_STRIP_TRAILING_WHITESPACE)

    if(CURRENT_GIT_URL MATCHES "^git@")
      set(MECH_COMM_LIB_GIT_URL "***********************:system_codebase/factory_tool/mech/mech_comm_lib.git")
    elseif(CURRENT_GIT_URL MATCHES "^http://")
      set(MECH_COMM_LIB_GIT_URL "http://gitlab.robosense.cn/system_codebase/factory_tool/mech/mech_comm_lib.git")
    elseif(CURRENT_GIT_URL MATCHES "^https://")
      set(MECH_COMM_LIB_GIT_URL "https://gitlab.robosense.cn/system_codebase/factory_tool/mech/mech_comm_lib.git")
    else()
      message(ERROR "Unsupported Git URL format: ${CURRENT_GIT_URL}")
      set(MECH_COMM_LIB_GIT_URL "http://gitlab.robosense.cn/system_codebase/factory_tool/mech/mech_comm_lib.git")
    endif()
  endif()
  include(FetchContent)
  set(FETCHCONTENT_QUIET OFF)
  FetchContent_Declare(
    mech_comm_lib
    GIT_REPOSITORY ${MECH_COMM_LIB_GIT_URL}
    GIT_TAG "develop"
    USES_TERMINAL_DOWNLOAD TRUE
    GIT_SHALLOW TRUE
    GIT_DEPTH 1
    GIT_CONFIG advice.detachedHead=false)
  FetchContent_MakeAvailable(mech_comm_lib)
  # 设置自定义下载路径
  set(FETCHCONTENT_BASE_DIR ${FETCHCONTENT_BASE_DIR_BAK})
  # include(FetchContent)
  # FetchContent_Declare(mech_comm_lib SOURCE_DIR
  #                                    /home/<USER>/development/projects/mech/airy_wave_signal_calib/lib/mech_comm_lib)
  # FetchContent_MakeAvailable(mech_comm_lib)
endif()

if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif()

if(NOT Boost_FOUND)
  if(WIN32)

    if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
      set(Boost_ARCHITECTURE "-x64")
    elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
      set(Boost_ARCHITECTURE "-x32")
    endif()

    set(Boost_USE_STATIC_LIBS ON)
    set(Boost_USE_MULTITHREADED ON)
    set(Boost_USE_STATIC_RUNTIME OFF)

    find_package(Boost REQUIRED CONFIG)

  elseif(Linux)
    find_package(
      Boost
      COMPONENTS system date_time
      REQUIRED)
  endif(WIN32)
endif()

# =========================
# Config Your Project Here
# =========================

if(MSVC)
  add_compile_options("/utf-8")
  add_definitions("-D_WIN32_WINNT=0x0A00")
else()
  add_compile_options(-Wall)
  # Set release and debug flags
  string(APPEND CMAKE_CXX_FLAGS_RELEASE " -O3")
  string(APPEND CMAKE_CXX_FLAGS_DEBUG " -O0 -ggdb")

  # Check and set specific flags for Clang
  if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    message(STATUS "Clang detected, setting specific flags")
    string(APPEND CMAKE_CXX_FLAGS_DEBUG " -fstandalone-debug")
  endif()
endif()

if(${CMAKE_BUILD_TYPE} MATCHES "Debug")
  message(STATUS "Debug flags are ${CMAKE_CXX_FLAGS_DEBUG}")
else()
  message(STATUS "Release flags are ${CMAKE_CXX_FLAGS_RELEASE}")
endif()

find_package(Threads REQUIRED QUIET)

find_package(Git QUIET)
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)

configure_file("${PROJECT_SOURCE_DIR}/src/config.h.in" "${PROJECT_SOURCE_DIR}/src/config.h")

# Add library source files
file(
  GLOB
  SOURCES
  CONFIGURE_DEPENDS
  "src/*.cpp"
  "src/protocol/*.cpp"
  "include/*.cpp"
  "include/protocol/*.cpp")

# Define the library
add_library(${PROJECT_NAME} STATIC ${SOURCES})

target_link_libraries(${PROJECT_NAME} rsfsc_comm_client RSFSCLog::RSFSCLog Threads::Threads ${Boost_LIBRARIES})
target_include_directories(${PROJECT_NAME} SYSTEM PUBLIC ${Boost_INCLUDE_DIRS})

# For the library users to find the headers
target_include_directories(${PROJECT_NAME} PUBLIC include)

# 配置boost采用UTF-8编码，在windows默认是使用GB2312编码，不配置会导致log乱码
target_compile_definitions(${PROJECT_NAME} PUBLIC BOOST_SYSTEM_USE_UTF8)
target_compile_options(${PROJECT_NAME} PRIVATE -fPIC)

# =========================
# Create Example
# =========================
if(BUILD_MECH_COMMUNICATION_EXAMPLE)
  add_subdirectory(example)
endif()

# =========================
# Create Unit Test
# =========================
if(BUILD_TEST)
  include(CTest)
  enable_testing()
  add_subdirectory(test)
endif(BUILD_TEST)
