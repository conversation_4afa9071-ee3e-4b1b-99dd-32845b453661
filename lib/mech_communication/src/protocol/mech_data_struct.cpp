/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mech_communication/protocol/data_struct/airy_pld.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/ip/address.hpp>
#include <boost/endian/conversion.hpp>
#include <sstream>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace mech
{
std::string ConfigPara::getSn() const { return fmt::format("{:02X}", fmt::join(sn, "")); }

uint32_t ConfigPara::getPsVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, ps_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getPlVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, pl_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getSoftwareVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, software_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getWebVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, web_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getMotorVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, motor_version.data(), 4);
  return temp;
}
std::string ConfigPara::getIpLocal() const { return net_info.getIpLocal(); }
uint16_t ConfigPara::getMsopPort() const { return net_info.getMsopPort(); }
uint16_t ConfigPara::getDifopPort() const { return net_info.getDifopPort(); }
std::string ConfigPara::getIpRemote() const { return net_info.getIpRemote(); }
std::string ConfigPara::getMacAddr() const { return net_info.getMacAddr(); }
std::string ConfigPara::getNetmaskLocal() const { return net_info.getNetmaskLocal(); }
std::string ConfigPara::getGatewayLocal() const { return net_info.getGatewayLocal(); }
uint16_t ConfigPara::getMotorRealTimeSpeed() const { return motor_real_time_speed; }
std::string NetPara::getIpLocal() const
{
  return fmt::format("{}.{}.{}.{}", ip_local[0], ip_local[1], ip_local[2], ip_local[3]);
}
uint16_t NetPara::getMsopPort() const { return msop_port; }
uint16_t NetPara::getDifopPort() const { return difop_port; }
std::string NetPara::getIpRemote() const
{
  return fmt::format("{}.{}.{}.{}", ip_remote[0], ip_remote[1], ip_remote[2], ip_remote[3]);
}
std::string NetPara::getMacAddr() const
{
  return fmt::format("{:02x}:{:02x}:{:02x}:{:02x}:{:02x}:{:02x}", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
}
std::string NetPara::getNetmaskLocal() const
{
  return fmt::format("{}.{}.{}.{}", netmask_local[0], netmask_local[1], netmask_local[2], netmask_local[3]);
}
std::string NetPara::getGatewayLocal() const
{
  return fmt::format("{}.{}.{}.{}", gateway_local[0], gateway_local[1], gateway_local[2], gateway_local[3]);
}

void NetPara::setIpLocal(const std::string& _ip)
{
  auto addr = boost::asio::ip::address::from_string(_ip).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), ip_local.begin());
}
void NetPara::setMsopPort(const uint16_t _port) { msop_port = _port; }
void NetPara::setDifopPort(const uint16_t _port) { difop_port = _port; }
void NetPara::setIpRemote(const std::string& _ip)
{
  auto addr = boost::asio::ip::address::from_string(_ip).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), ip_remote.begin());
}
void NetPara::setMacAddr(std::string _mac)
{
  std::vector<uint8_t> mac_addr;
  std::replace(_mac.begin(), _mac.end(), ':', ' ');
  std::istringstream iss(_mac);
  std::string token;
  while (std::getline(iss, token, ' '))
  {
    mac_addr.push_back(static_cast<uint8_t>(std::stoul(token, nullptr, 16)));
  }
  std::copy(mac_addr.begin(), mac_addr.end(), mac.begin());
}
void NetPara::setNetmaskLocal(const std::string& _netmask)
{
  auto addr = boost::asio::ip::address::from_string(_netmask).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), netmask_local.begin());
}
void NetPara::setGatewayLocal(const std::string& _gateway)
{
  auto addr = boost::asio::ip::address::from_string(_gateway).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), gateway_local.begin());
}

void DifopPacket::toBigEndian()
{
  pkt_head               = boost::endian::native_to_big(pkt_head);
  motor_set_speed        = boost::endian::native_to_big(motor_set_speed);
  ip_src                 = boost::endian::native_to_big(ip_src);
  ip_dst                 = boost::endian::native_to_big(ip_dst);
  msop_port              = boost::endian::native_to_big(msop_port);
  difop_port             = boost::endian::native_to_big(difop_port);
  fov_start              = boost::endian::native_to_big(fov_start);
  fov_end                = boost::endian::native_to_big(fov_end);
  lock_phase             = boost::endian::native_to_big(lock_phase);
  top_firmware_version   = boost::endian::native_to_big(top_firmware_version);
  bot_firmware_version   = boost::endian::native_to_big(bot_firmware_version);
  app_firmware_version   = boost::endian::native_to_big(app_firmware_version);
  motor_firmware_version = boost::endian::native_to_big(motor_firmware_version);
  cgi_firmware_version   = boost::endian::native_to_big(cgi_firmware_version);
  zero_angle             = boost::endian::native_to_big(zero_angle);
  for (size_t i = 0; i < time_sec.size() / 2; ++i)
  {
    uint8_t tmp                          = time_sec.at(i);
    time_sec.at(i)                       = time_sec.at(time_sec.size() - 1 - i);
    time_sec.at(time_sec.size() - 1 - i) = tmp;
  }
  time_nano         = boost::endian::native_to_big(time_nano);
  total_run_time    = boost::endian::native_to_big(total_run_time);
  reboot_count      = boost::endian::native_to_big(reboot_count);
  realtime_phase    = boost::endian::native_to_big(realtime_phase);
  realtime_speed    = boost::endian::native_to_big(realtime_speed);
  top_input_vol     = boost::endian::native_to_big(top_input_vol);
  top_3v8           = boost::endian::native_to_big(top_3v8);
  top_3v3           = boost::endian::native_to_big(top_3v3);
  top_1v1           = boost::endian::native_to_big(top_1v1);
  top_neg_vol       = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(top_neg_vol)));
  top_3v3_rx        = boost::endian::native_to_big(top_3v3_rx);
  top_charge_vol    = boost::endian::native_to_big(top_charge_vol);
  total_input_vol   = boost::endian::native_to_big(total_input_vol);
  bot_12v           = boost::endian::native_to_big(bot_12v);
  bot_mcu_0v85      = boost::endian::native_to_big(bot_mcu_0v85);
  bot_fpga_1v       = boost::endian::native_to_big(bot_fpga_1v);
  total_input_cur   = boost::endian::native_to_big(total_input_cur);
  top_fpga_temp     = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(top_fpga_temp)));
  top_tx_temp       = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(top_tx_temp)));
  top_rx_459_temp_n = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(top_rx_459_temp_n)));
  top_rx_459_temp_p = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(top_rx_459_temp_p)));
  bot_imu_temp      = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(bot_imu_temp)));
  bot_fpga_temp     = static_cast<int16_t>(boost::endian::native_to_big(static_cast<uint16_t>(bot_fpga_temp)));
  total_power       = boost::endian::native_to_big(total_power);
  tail              = boost::endian::native_to_big(tail);
}
bool DifopPacket::isValid()
{
  if (pkt_head == DIFOP_PKT_HEAD && tail == DIFOP_PKT_TAIL)
  {
    return true;
  }
  if (boost::endian::native_to_big(pkt_head) == DIFOP_PKT_HEAD && boost::endian::native_to_big(tail) == DIFOP_PKT_TAIL)
  {
    toBigEndian();
    return true;
  }
  return false;
}

void DistArea::toBigEndian() { dist = boost::endian::native_to_big(dist); }
void DataBlock::toBigEndian()
{
  ide     = boost::endian::native_to_big(ide);
  azimuth = boost::endian::native_to_big(azimuth);
  for (auto& dist_refl : dist_refl)
  {
    dist_refl.toBigEndian();
  }
}
bool MsopPacket::isValid()
{
  if (pkt_head == MSOP_PKT_HEAD)
  {
    return true;
  }
  if (boost::endian::native_to_big(pkt_head) == MSOP_PKT_HEAD)
  {
    toLittleEndian();
    return true;
  }
  return false;
}
void MsopPacket::toBigEndian()
{
  pkt_head          = boost::endian::native_to_big(pkt_head);
  rev0              = boost::endian::native_to_big(rev0);
  pktcnt_top_to_bot = boost::endian::native_to_big(pktcnt_top_to_bot);
  pktcnt_bot_tops   = boost::endian::native_to_big(pktcnt_bot_tops);
  data_type         = boost::endian::native_to_big(data_type);
  rev1              = boost::endian::native_to_big(rev1);
  timestamp_nano    = boost::endian::native_to_big(timestamp_nano);
  rev2              = boost::endian::native_to_big(rev2);
  lidar_type        = boost::endian::native_to_big(lidar_type);
  lidar_model       = boost::endian::native_to_big(lidar_model);
  rx_temp           = boost::endian::native_to_big(rx_temp);
  top_board_temp    = boost::endian::native_to_big(top_board_temp);
  for (auto& block : data_block)
  {
    block.toBigEndian();
  }
  for (size_t i = 0; i < timestamp_sec.size() / 2; ++i)
  {
    uint8_t tmp                                    = timestamp_sec.at(i);
    timestamp_sec.at(i)                            = timestamp_sec.at(timestamp_sec.size() - 1 - i);
    timestamp_sec.at(timestamp_sec.size() - 1 - i) = tmp;
  }
}

void DynamicComp::toBigEndian()
{
  for (auto& dist : dist_val_arr)
  {
    dist = boost::endian::native_to_big(dist);
  }
}

void DynamicBit::toBigEndian()
{
  for (auto& com : comp)
  {
    com.toBigEndian();
  }
  for (auto& reg : gdi_reg)
  {
    reg.reg_addr_val = boost::endian::native_to_big(reg.reg_addr_val);
    reg.reg_addr     = boost::endian::native_to_big(reg.reg_addr);
  }
}
void StaticBit::toBigEndian()
{
  for (auto& com : comp)
  {
    com.code4_dist = boost::endian::native_to_big(com.code4_dist);
    com.code3_dist = boost::endian::native_to_big(com.code3_dist);
    com.code2_dist = boost::endian::native_to_big(com.code2_dist);
    com.code1_dist = boost::endian::native_to_big(com.code1_dist);
  }
}
void DistAreaU16::toBigEndian()
{
  dist = boost::endian::native_to_big(dist);
  area = boost::endian::native_to_big(area);
}
void AreaComp::toBigEndian()
{
  refl_10  = boost::endian::native_to_big(refl_10);
  refl_40  = boost::endian::native_to_big(refl_40);
  refl_90  = boost::endian::native_to_big(refl_90);
  refl_255 = boost::endian::native_to_big(refl_255);
}
void AbsBit::toBigEndian()
{
  for (auto& arr : abs_coe_arr)
  {
    for (auto& coe : arr)
    {
      coe.abs_coe_k = boost::endian::native_to_big(coe.abs_coe_k);
      coe.abs_coe_b = boost::endian::native_to_big(coe.abs_coe_b);
    }
  }
}

}  // namespace mech

namespace airy_pld
{
void DifopPacket::toBigEndian()
{
  pkt_head               = boost::endian::native_to_big(pkt_head);
  motor_set_speed        = boost::endian::native_to_big(motor_set_speed);
  ip_src                 = boost::endian::native_to_big(ip_src);
  ip_dst                 = boost::endian::native_to_big(ip_dst);
  msop_port              = boost::endian::native_to_big(msop_port);
  difop_port             = boost::endian::native_to_big(difop_port);
  fov_start              = boost::endian::native_to_big(fov_start);
  fov_end                = boost::endian::native_to_big(fov_end);
  lock_phase             = boost::endian::native_to_big(lock_phase);
  top_firmware_version   = boost::endian::native_to_big(top_firmware_version);
  bot_firmware_version   = boost::endian::native_to_big(bot_firmware_version);
  app_firmware_version   = boost::endian::native_to_big(app_firmware_version);
  motor_firmware_version = boost::endian::native_to_big(motor_firmware_version);
  cgi_firmware_version   = boost::endian::native_to_big(cgi_firmware_version);
  zero_angle             = boost::endian::native_to_big(zero_angle);
  for (size_t i = 0; i < time_sec.size() / 2; ++i)
  {
    uint8_t tmp                          = time_sec.at(i);
    time_sec.at(i)                       = time_sec.at(time_sec.size() - 1 - i);
    time_sec.at(time_sec.size() - 1 - i) = tmp;
  }
  time_nano      = boost::endian::native_to_big(time_nano);
  realtime_phase = boost::endian::native_to_big(realtime_phase);
  realtime_speed = boost::endian::native_to_big(realtime_speed);
  total_run_time = boost::endian::native_to_big(total_run_time);
  reboot_count   = boost::endian::native_to_big(reboot_count);
  tail           = boost::endian::native_to_big(tail);
  for (size_t i = 0; i < gprmc.size() / 2; ++i)
  {
    uint8_t tmp                    = gprmc.at(i);
    gprmc.at(i)                    = gprmc.at(gprmc.size() - 1 - i);
    gprmc.at(gprmc.size() - 1 - i) = tmp;
  }
  start_time = boost::endian::native_to_big(start_time);
  for (size_t i = 0; i < vertical_calib.size() / 2; ++i)
  {
    uint8_t tmp                                      = vertical_calib.at(i);
    vertical_calib.at(i)                             = vertical_calib.at(vertical_calib.size() - 1 - i);
    vertical_calib.at(vertical_calib.size() - 1 - i) = tmp;
  }
  for (size_t i = 0; i < horizontal_calib.size() / 2; ++i)
  {
    uint8_t tmp                                          = horizontal_calib.at(i);
    horizontal_calib.at(i)                               = horizontal_calib.at(horizontal_calib.size() - 1 - i);
    horizontal_calib.at(horizontal_calib.size() - 1 - i) = tmp;
  }
  bot_vbus       = boost::endian::native_to_big(bot_vbus);
  bot_ibus       = boost::endian::native_to_big(bot_ibus);
  bot_sys_5v     = boost::endian::native_to_big(bot_sys_5v);
  bot_sys_12v    = boost::endian::native_to_big(bot_sys_12v);
  bot_vcco_psio0 = boost::endian::native_to_big(bot_vcco_psio0);
  bot_sys_1v2    = boost::endian::native_to_big(bot_sys_1v2);
  bot_vcco_psddr = boost::endian::native_to_big(bot_vcco_psddr);
  top_vbus       = boost::endian::native_to_big(top_vbus);
  top_sys_3v8    = boost::endian::native_to_big(top_sys_3v8);
  top_sys_3v3    = boost::endian::native_to_big(top_sys_3v3);
  top_sys_2v5    = boost::endian::native_to_big(top_sys_2v5);
  top_sys_1v1    = boost::endian::native_to_big(top_sys_1v1);
  top_rx_vbd     = boost::endian::native_to_big(top_rx_vbd);
  top_tx_charge  = boost::endian::native_to_big(top_tx_charge);
  top_sys_1v8    = boost::endian::native_to_big(top_sys_1v8);
  top_sys_1v0    = boost::endian::native_to_big(top_sys_1v0);
  bot_psintlp    = boost::endian::native_to_big(bot_psintlp);
  bot_psintfp    = boost::endian::native_to_big(bot_psintfp);
  bot_ps_aux     = boost::endian::native_to_big(bot_ps_aux);
  bot_pl_vccint  = boost::endian::native_to_big(bot_pl_vccint);
  total_power    = boost::endian::native_to_big(total_power);
  tail           = boost::endian::native_to_big(tail);
}
[[nodiscard]] bool DifopPacket::isValid()
{
  if (pkt_head == mech::DIFOP_PKT_HEAD && tail == mech::DIFOP_PKT_TAIL)
  {
    return true;
  }
  if (boost::endian::native_to_big(pkt_head) == mech::DIFOP_PKT_HEAD &&
      boost::endian::native_to_big(tail) == mech::DIFOP_PKT_TAIL)
  {
    toBigEndian();
    return true;
  }
  return false;
}
}  // namespace airy_pld

}  // namespace lidar
}  // namespace robosense