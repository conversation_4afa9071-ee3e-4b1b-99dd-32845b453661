﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_DATA_STRUCT_H
#define MECH_DATA_STRUCT_H
#include <array>
#include <cstdint>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
namespace mech
{
#pragma pack(push, 1)
enum class GpsBaudRate : uint8_t
{
  GPS_BAUD_1200    = 0x00,
  GPS_BAUD_2400    = 0x01,
  GPS_BAUD_4800    = 0x02,
  GPS_BAUD_9600    = 0x03,
  GPS_BAUD_14400   = 0x04,
  GPS_BAUD_19200   = 0x05,
  GPS_BAUD_38400   = 0x06,
  GPS_BAUD_43000   = 0x07,
  GPS_BAUD_57600   = 0x08,
  GPS_BAUD_76800   = 0x09,
  GPS_BAUD_115200  = 0x0A,
  GPS_BAUD_128000  = 0x0B,
  GPS_BAUD_230400  = 0x0C,
  GPS_BAUD_256000  = 0x0D,
  GPS_BAUD_460800  = 0x0E,
  GPS_BAUD_921600  = 0x0F,
  GPS_BAUD_1382400 = 0x10
};
enum class EchoMode : uint8_t
{
  STRONGEST = 0x01,  // 最强回波
  FIRST     = 0x03,  // 最近回波
  LAST      = 0x02,  // 最后回波
  DUAL_ECHO = 0x00,  // 双回波
};
enum class TimeSyncMode : uint8_t
{
  GPS    = 0x00,
  E2E    = 0x01,
  P2P    = 0x02,
  GPTP   = 0x03,
  E2E_L2 = 0x04
};
enum class MountType : uint8_t
{
  FRONT,    // 正装
  SIDE,     // 侧装
  MAPPING,  // 测绘
  RESERVE,  // 保留
  MOW,      // 割草版
  UNKNOWN
};
struct GpsStatus
{
  uint8_t pps_lock : 1;
  uint8_t gprmc_lock : 1;
  uint8_t utc_lock : 1;
  uint8_t gprmc_input : 1;
  uint8_t pps_input : 1;
  uint8_t reserve_gps : 3;
};

enum class ResponseType
{
  SUCCESS                                  = 0x00,  // 成功
  UNSUPPORTED                              = 0x01,  // 不支持
  PARAMETER_ERROR                          = 0x02,  // 参数错误
  DATA_LEN_ERROR                           = 0x03,  // 数据长度错误
  FORMAT_ERROR                             = 0x04,  // 格式错误
  CHECKSUM_ERROR                           = 0x05,  // 校验和错误
  OTHER                                    = 0x06,  // 其他错误
  TIMEOUT                                  = 0x07,  // 超时
  CURRENT_STATUS                           = 0x08,  // 当前状态
  VERSION_UNSUPPORTED                      = 0x09,  // 版本不支持
  TOP_LINK_ERR_1                           = 0x0a,  // 顶板链接错误1
  TOP_LINK_ERR_2                           = 0x0b,  // 顶板链接错误2
  TOP_ERASE_ERR_1                          = 0x0c,  // 顶板擦除错误1
  TOP_ERASE_ERR_2                          = 0x0d,  // 顶板擦除错误2
  SEQUENCE_ERR                             = 0x0e,  // 包序号错误
  TOP_WRITE_ERR_1                          = 0x0f,  // 顶板写入错误1
  TOP_WRITE_ERR_2                          = 0x10,  // 顶板写入错误2
  COMM_REQ_ERR                             = 0x11,  // 通信请求错误
  COMM_FILE_SIZE_ERR                       = 0x12,  // 文件大小错误
  COMM_ERASE_FLS_ERR                       = 0x13,  // 擦除flash错误
  COMM_DATA_LEN_ERR                        = 0x14,  // 数据长度错误
  COMM_FIRMWARE_TYPE_ERR                   = 0x15,  // 固件类型错误
  COMM_OP_FLS_ERR                          = 0x16,  // flash操作错误
  COMM_COMP_CHK_ERR                        = 0x17,  // 兼容性校验错误
  COMM_OP_FLS_CMP_ERR                      = 0x18,  // flash操作回读错误
  PS2MOT_START_CODE_WHEEL_CALI_FAILED      = 0x19,  // 电机发送开始标定指令失败
  MOT2PS_MOT_CODE_WHEEL_CALI_STATUS_FAILED = 0x1a,  // 电机发送获取标定状态失败
  MOT_CODE_WHEEL_CALI_TIMEOUT              = 0x1b,  // 标定返回超时
  MOT2PS_MOT_E_ZERO_FAILED                 = 0x1c,  // 电机发送E-zero指令失败
  MOT_CODE_WHEEL_CALI_E_ZERO_TIMEOUT       = 0x1d,  // E-zero获取超时
};

struct Version
{
  uint32_t pl_version;
  uint32_t ps_version;
  uint32_t app_version;
  uint32_t motor_version;
  uint32_t config_version;
};

constexpr uint64_t DIFOP_PKT_HEAD = 0xA5FF005A11115555;
constexpr uint16_t DIFOP_PKT_TAIL = 0x0ff0;
struct DifopPacket
{
  uint64_t pkt_head;                // 0x55aa055a
  uint16_t motor_set_speed;         // 电机设置转速 300/600/1200
  uint32_t ip_src;                  // 以太网IP源地址
  uint32_t ip_dst;                  // 以太网IP目标地址
  std::array<uint8_t, 6> mac_addr;  // 雷达MAC地址
  uint16_t msop_port;               // MSOP端口
  uint16_t reserve;
  uint16_t difop_port;  // DIFOP端口
  uint16_t reserve1;
  uint16_t fov_start;  // FOV起始角度, 0-359, 精度0.01°
  uint16_t fov_end;    // FOV结束角度, 0-359, 精度0.01°
  uint16_t reserve2;
  uint16_t lock_phase;                // 锁相相位, 0-360, 精度1°
  uint8_t top_firmware_reserve;       // 主板固件版本
  uint32_t top_firmware_version;      // 主板固件版本
  uint8_t bot_firmware_reserve;       // 底板固件版本
  uint32_t bot_firmware_version;      // 底板固件版本
  uint8_t app_firmware_reserve;       // APP固件版本
  uint32_t app_firmware_version;      // APP固件版本
  uint8_t motor_firmware_reserve;     // 电机固件版本
  uint32_t motor_firmware_version;    // 电机固件版本
  uint8_t cgi_firmware_reserve;       // cgi固件版本
  uint32_t cgi_firmware_version;      // cgi固件版本
  std::array<uint8_t, 223> reserve3;  // 预留
  GpsBaudRate gps_baud_rate;          // GPS同步模式下的GPRMC波特率
  MountType mount_type;               // 安装方式, 0:正装, 1:侧装, 2:测绘
  std::array<uint8_t, 2> reserve4;
  std::array<uint8_t, 6> sn;    // 产品序列号
  uint16_t zero_angle;          // 零度角标定值, 0-359.99, 单位0.01度
  EchoMode echo_mode;           // 回波模式
  TimeSyncMode time_sync_mode;  // 时间同步方式设置
  uint8_t time_sync_status;     // 时间同步状态, 0x00:未同步, 0x01:同步成功
  // std::array<uint8_t, 10> time;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  std::array<uint8_t, 6> time_sec;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  uint32_t time_nano;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  uint8_t reserve5;
  std::array<uint8_t, 19> reserve6;
  std::array<uint8_t, 4> reserve7;
  uint8_t motor_dir;        // 电机正转反转标志, 0x00:正转, 0x01:反转
  uint32_t total_run_time;  // 设备运行总时间, 单位为分钟, 溢出后重新统计
  std::array<uint8_t, 9> reserve8;
  uint16_t reboot_count;  // 设备启动次数, 1-65535循环计数
  std::array<uint8_t, 4> reserve9;
  GpsStatus gps_status;  // GPS状态
  std::array<uint8_t, 8> reserve10;
  std::array<uint8_t, 5> reserve11;
  uint16_t realtime_phase;  // 实时相位, 单位度
  uint16_t realtime_speed;  // 电机实时转速, 单位RPM
  uint32_t start_time;      // 电机启动时间, 单位ms
  std::array<uint8_t, 3> reserve12;
  std::array<uint8_t, 86> gprmc;              // GPRMC
  std::array<uint8_t, 288> vertical_calib;    // 垂直角校准
  std::array<uint8_t, 288> horizontal_calib;  // 水平角校准
  uint16_t top_input_vol;                     // 主板总输入电压, DIFOP: Data/100, 阈值范围: 9~32V
  uint16_t top_3v8;                           // 主板3.8v电压, DIFOP: Data/100, 阈值范围: 3.5~4.1V
  uint16_t top_3v3;                           // 主板3.3v电压, DIFOP: Data/100, 阈值范围: 3~3.6V
  uint16_t reserve13;
  uint16_t reserve14;
  uint16_t top_1v1;  // 主板接收1.1V电压, DIFOP: Data/100, 阈值范围: 1.0~1.2V
  uint16_t reserve15;
  int16_t top_neg_vol;      // 主板接收负压, DIFOP: Data/100, 阈值范围: -25V~-11V
  uint16_t top_3v3_rx;      // 主板接收3.3V电压, DIFOP: Data/100, 阈值范围: 3.2V~3.8V
  uint16_t top_charge_vol;  // 主板发射充能电压, DIFOP: Data/100, 阈值范围: 15V~35V
  uint16_t reserve16;
  uint16_t total_input_vol;  // 整机输入电压, DIFOP: Data/100, 阈值范围: 9V~32V
  uint16_t bot_12v;          // 底板12V电压, DIFOP: Data/100, 阈值范围: 11V~13V
  uint16_t bot_mcu_0v85;     // 底板MCU0.85V电压, DIFOP: Data/100, 阈值范围: 0.8V~0.9V
  uint16_t bot_fpga_1v;      // 底板FPGA内核1V, DIFOP: Data/100, 阈值范围: 0.9V~1.1V
  uint16_t total_input_cur;  // 整机输入电流, DIFOP: Data/100, 整机输入电压*总输入电流
  int16_t top_fpga_temp;     // 主板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
  uint16_t reserve17;
  int16_t top_tx_temp;        // 主板发射温度, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t top_rx_459_temp_n;  // 主板RX-459温度N端, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t top_rx_459_temp_p;  // 主板RX-459温度P端, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t bot_imu_temp;       // 底板IMU温度, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t bot_fpga_temp;      // 底板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
  uint16_t total_power;       // 整机功率, DIFOP: Data/100, 整机输入电压*总输入电流
  float q_x;                  // imu标定数据
  float q_y;                  // imu标定数据
  float q_z;                  // imu标定数据
  float q_w;                  // imu标定数据
  float x;                    // imu标定数据
  float y;                    // imu标定数据
  float z;                    // imu标定数据
  std::array<uint8_t, 126> reserve18;
  uint16_t tail;  // 帧尾, 0x0F 0xF0

  void toBigEndian();
  [[nodiscard]] bool isValid();

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  [[nodiscard]] const char* data() const { return reinterpret_cast<const char*>(this); }
};

#pragma pack(pop)

constexpr uint32_t MAX_REG_ADDR     = 0x83D00000;
constexpr uint32_t MIN_REG_ADDR     = 0x83C00000;
constexpr uint32_t MIN_TOP_REG_ADDR = 0x1000;
constexpr uint32_t MAX_TOP_REG_ADDR = 0x4000;

constexpr uint32_t CONFIG_VERSION_ADDR = 0x83C0000C;

// 参考mems通信库，声明状态码，命令类型
constexpr uint16_t FRAME_FLAG        = 0xffff;
constexpr uint8_t FRAME_TAIL_FLAG    = 0xfe;
constexpr uint8_t RUBY_NET_CMD_BEGIN = 0x00;
constexpr uint8_t FRAME_TYPE_REQUEST = 0x00;
constexpr uint8_t FRAME_TYPE_ACK     = 0x01;

constexpr uint16_t NET_CMD_BEGIN = 0x1000;

// 参数设置
constexpr uint16_t NET_CMD_CONFIG_SET_SN               = 0x1001;
constexpr uint16_t NET_CMD_CONFIG_SET_NETWORK          = 0x1002;
constexpr uint16_t NET_CMD_CONFIG_SET_TIME_SYNC_MODE   = 0x1003;
constexpr uint16_t NET_CMD_CONFIG_SET_ECHO_WAVE_MODE   = 0x1004;
constexpr uint16_t NET_CMD_CONFIG_SET_FOV_ANGLE        = 0x1005;
constexpr uint16_t NET_CMD_CONFIG_SET_PHASE_LOCK       = 0x1006;
constexpr uint16_t NET_CMD_CONFIG_SET_MOTOR_SPEED      = 0x1007;
constexpr uint16_t NET_CMD_CONFIG_SET_ANGLE_TRIGGER    = 0x1008;
constexpr uint16_t NET_CMD_CONFIG_SET_LIDAR_MODE       = 0x1009;
constexpr uint16_t NET_CMD_CONFIG_SET_RAIN_FOG_REMOVAL = 0x100a;
constexpr uint16_t NET_CMD_CONFIG_SET_ZERO_ANGLE       = 0x100b;
constexpr uint16_t NET_CMD_CONFIG_SET_MULTI_PARAM      = 0x10ff;

// 参数获取
constexpr uint16_t NET_CMD_CONFIG_READ_ALL = 0x2001;
constexpr uint16_t NET_CMD_PS_DEFINED      = 0x2002;

// 升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_ACK = 0x3001;
// 固件升级查询
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_CHECK = 0x3002;
// 补丁包升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_PATCH = 0x3003;
// 补充升级config
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_CONFIG = 0x3004;
// app升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_APP = 0x3005;
// sbl升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_SBL = 0x3006;
// bot升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_BOT = 0x3007;
// top升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_TOP = 0x3008;

// 电机控制
constexpr uint16_t NET_CMD_MOTOR_SEND_CMD    = 0x4001;
constexpr uint16_t NET_CMD_MOTOR_CALIBRATION = 0x4002;

// 顶板
constexpr uint16_t NET_CMD_TOP_BOARD_CON_READ_REGISTER    = 0x5001;
constexpr uint16_t NET_CMD_TOP_BOARD_CON_WRITE_REGISTER   = 0x5002;
constexpr uint16_t NET_CMD_TOP_BOARD_WRITE_FLASH          = 0x5003;
constexpr uint16_t NET_CMD_TOP_BOARD_READ_FLASH           = 0x5004;
constexpr uint16_t NET_CMD_TOP_BOARD_PARAM_UPDATE         = 0x5005;
constexpr uint16_t NET_CMD_TOP_BOARD_EYES_SAFE            = 0x5006;
constexpr uint16_t NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER = 0x5007;
constexpr uint16_t NET_CMD_TOP_BOARD_MULTI_READ_REGISTER  = 0x5008;
constexpr uint16_t NET_CMD_TOP_BOARD_GET_INTENSITY        = 0x5009;

// 底板
constexpr uint16_t NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER    = 0x6001;  // 0x6001 读底板寄存器
constexpr uint16_t NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER   = 0x6002;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_PARAM_UPDATE         = 0x6003;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER = 0x6004;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER  = 0x6005;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_WRITE_ZERO_ANGLE     = 0x6006;  // 写入零度角
constexpr uint16_t NET_CMD_BOTTOM_BOARD_READ_ZERO_ANGLE      = 0x6007;  // 读取零度角

// 其他
constexpr uint16_t NET_CMD_OTHER_CHANNEL_ANGLE_WRITE = 0xe001;
constexpr uint16_t NET_CMD_OTHER_CHANNEL_ANGLE_READ  = 0xe002;
constexpr uint16_t NET_CMD_OTHER_MONITOR_DATA        = 0xe003;
constexpr uint16_t NET_CMD_OTHER_FIRM_459_WRITE      = 0xe013;
constexpr uint16_t NET_CMD_OTHER_FIRM_459_READ       = 0xe014;
constexpr uint16_t NET_CMD_OTHER_FIRM_TOP_WRITE      = 0xe015;
constexpr uint16_t NET_CMD_OTHER_FIRM_BOT_WRITE      = 0xe016;
constexpr uint16_t NET_CMD_OTHER_ENCOD_CALIB_READ    = 0xe018;
constexpr uint16_t NET_CMD_OTHER_FIRM_TOP_READ       = 0xe019;
constexpr uint16_t NET_CMD_OTHER_FIRM_BOT_READ       = 0xe01a;

constexpr uint16_t NET_CMD_END = 0xf000;

constexpr uint8_t LIDAR_TYPE_RUBY   = 0x05;
constexpr uint8_t LIDAR_TYPE_BPEARL = 0x03;

constexpr uint8_t FLASH_CMD_REQ_WRITE    = 0xC1;
constexpr uint8_t FLASH_CMD_WRITE        = 0xC2;
constexpr uint8_t FLASH_CMD_WRITE_FINISH = 0xC3;

#pragma pack(push, 1)
struct FrameHead
{
  uint16_t frame_header;
  uint8_t frame_type;
  uint16_t length;
  uint16_t cmd_type;
  uint8_t response_type;
};

namespace server
{
struct FrameHead
{
  uint16_t frame_header;
  uint8_t frame_type;
  uint16_t length;
  uint16_t cmd_type;
};
}  // namespace server
struct NetPara
{
  std::array<uint8_t, 6> mac;

  std::array<uint8_t, 4> ip_local;
  std::array<uint8_t, 4> netmask_local;
  std::array<uint8_t, 4> gateway_local;

  uint16_t msop_port;
  uint16_t difop_port;

  std::array<uint8_t, 4> ip_remote;

  [[nodiscard]] std::string getIpLocal() const;
  [[nodiscard]] uint16_t getMsopPort() const;
  [[nodiscard]] uint16_t getDifopPort() const;
  [[nodiscard]] std::string getIpRemote() const;
  [[nodiscard]] std::string getMacAddr() const;
  [[nodiscard]] std::string getNetmaskLocal() const;
  [[nodiscard]] std::string getGatewayLocal() const;

  void setIpLocal(const std::string& _ip);
  void setMsopPort(const uint16_t _port);
  void setDifopPort(const uint16_t _port);
  void setIpRemote(const std::string& _ip);
  void setMacAddr(std::string _mac);
  void setNetmaskLocal(const std::string& _netmask);
  void setGatewayLocal(const std::string& _gateway);
};

struct ConfigPara
{
  std::array<uint8_t, 6> sn;
  NetPara net_info;

  TimeSyncMode time_sync_mode;
  uint8_t time_sync_status;
  EchoMode echo_wave_mode;

  std::array<uint8_t, 4> pl_version;
  std::array<uint8_t, 4> ps_version;
  std::array<uint8_t, 4> software_version;
  std::array<uint8_t, 4> web_version;
  std::array<uint8_t, 4> motor_version;

  float angle0;

  uint16_t start_fov;
  uint16_t end_fov;

  uint8_t motor_speed;
  uint16_t motor_phase;

  uint8_t status_of_code_wheel_cali;
  uint16_t status_of_phase_lock;

  uint16_t motor_real_time_speed;
  uint16_t motor_real_time_phase;

  [[nodiscard]] std::string getSn() const;
  [[nodiscard]] uint32_t getPsVersion() const;
  [[nodiscard]] uint32_t getPlVersion() const;
  [[nodiscard]] uint32_t getSoftwareVersion() const;
  [[nodiscard]] uint32_t getWebVersion() const;
  [[nodiscard]] uint32_t getMotorVersion() const;
  [[nodiscard]] std::string getIpLocal() const;
  [[nodiscard]] uint16_t getMsopPort() const;
  [[nodiscard]] uint16_t getDifopPort() const;
  [[nodiscard]] std::string getIpRemote() const;
  [[nodiscard]] std::string getMacAddr() const;
  [[nodiscard]] std::string getNetmaskLocal() const;
  [[nodiscard]] std::string getGatewayLocal() const;
  [[nodiscard]] uint16_t getMotorRealTimeSpeed() const;
};

struct DistArea
{
  uint16_t dist;
  uint8_t refl;
  void toBigEndian();
};
struct DataBlock
{
  uint16_t ide;
  uint16_t azimuth;
  std::array<DistArea, 48> dist_refl;
  void toBigEndian();
};

constexpr uint32_t MSOP_PKT_HEAD = 0x55aa055a;
struct MsopPacket
{
  uint32_t pkt_head;
  uint32_t rev0;
  uint32_t pktcnt_top_to_bot;
  uint32_t pktcnt_bot_tops;
  uint16_t data_type;
  uint16_t rev1;
  std::array<uint8_t, 6> timestamp_sec;
  uint32_t timestamp_nano;
  uint8_t rev2;
  uint8_t lidar_type;
  uint8_t lidar_model;
  std::array<uint8_t, 5> rev3;
  uint16_t rx_temp;
  uint16_t top_board_temp;
  std::array<DataBlock, 8> data_block;
  std::array<uint8_t, 6> tails;
  std::array<uint8_t, 16> rev4;

  void toBigEndian();
  void toLittleEndian() { toBigEndian(); }
  char* data() { return reinterpret_cast<char*>(this); }
  [[nodiscard]] bool isValid();
};

struct GainData
{
  uint32_t addr;
  uint32_t value;

  uint32_t addr_l;
  uint32_t value_l;

  uint32_t addr_h;
  uint32_t value_h;
};

struct LockData
{
  uint32_t addr_0;
  uint32_t value_0;
  uint32_t addr_1;
  uint32_t value_1;
};

struct AreaDistData
{
  LockData lock_data;
  uint32_t area_addr_h;
  uint32_t area_value_h;
  uint32_t area_addr_l;
  uint32_t area_value_l;

  uint32_t dist_addr_h;
  uint32_t dist_value_h;
  uint32_t dist_addr_l;
  uint32_t dist_value_l;
};

struct GainIntensityData
{
  GainData gain_data;
  std::array<AreaDistData, 2> area_dist_data;
};

struct IntensityData
{
  std::array<GainIntensityData, 16> gain_intensity_data;
};
struct GainDistAreaData
{
  uint16_t gain;
  uint16_t distance;
  uint16_t area;
};

struct GdiRegU8
{
  uint8_t reg_flag;
  uint8_t reg_addr_high;
  uint8_t reg_addr_low;
  uint8_t reg_data;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint8_t _data)
  {
    reg_flag      = (_addr >> 16U) & 0xffU;
    reg_addr_high = (_addr >> 8U) & 0xffU;
    reg_addr_low  = _addr & 0xffU;
    reg_data      = _data;
  }
};
struct GdiRegU16
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint16_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 1, _data & 0xffU);
  }
};
struct GdiRegU24
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_mid;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint32_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 16U) & 0xffU);
    reg_mid.setData(_addr + 1, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 2, _data & 0xffU);
  }
};

constexpr uint32_t DYNAMIC_GDI_REG_START = 0x8c2170;
// constexpr uint8_t ABS_GDI_REG_START      = 0x50;
constexpr uint32_t GDI_REG_STATIC_ENABLE  = 0x8c2027;
constexpr uint32_t GDI_REG_TEMPERATURE    = 0x8c2017;
constexpr uint32_t GDI_REG_ABS_DIST_START = 0x8c2150;

union DynamicGdiReg
{
  // 前三个字节为地址，最后低位字节为数据
  struct
  {
    uint16_t reg_addr;
    uint16_t reg_addr_val;
  };

  uint32_t reg_data;
};

constexpr std::array<uint16_t, 512> getDynamicCalibAreaArray()
{
  std::array<uint16_t, 512> arr = { 65535 };
  uint16_t index                = 0;
  // 步进64，从0开始，到32704结束
  for (uint16_t area = 0; area < 32768; area += 64)
  {
    arr.at(index++) = area;
  }
  return arr;
}
constexpr std::array<uint16_t, 512> DYNAMIC_COMP_AREA_ARR = getDynamicCalibAreaArray();
struct DynamicComp
{
  std::array<uint16_t, DYNAMIC_COMP_AREA_ARR.size()> dist_val_arr;

  void toBigEndian();
};
union DynamicBit
{
  struct
  {
    std::array<DynamicComp, 96> comp;
    std::array<DynamicGdiReg, 12> gdi_reg;
  };

  std::array<char, static_cast<std::size_t>(192 * 1024)> arr;
  void toBigEndian();
};

struct StaticComp
{
  uint16_t code4_dist;
  uint16_t code3_dist;
  uint16_t code2_dist;
  uint16_t code1_dist;
};

union StaticBit
{
  struct
  {
    union
    {
      std::array<StaticComp, 96> comp;
      std::array<char, 1024> comp_arr;
    };
    std::array<GdiRegU8, 2> reg;
  };

  std::array<char, static_cast<std::size_t>(2 * 1024)> arr;

  void toBigEndian();
};

struct DistAreaU16
{
  int16_t dist;
  uint16_t area;
  void toBigEndian();
};

struct TwoDimAbsComp
{
  std::array<DistAreaU16, 16> dist_area_arr;
};
struct TwoDimAbsBoard
{
  std::array<TwoDimAbsComp, 8> comp_arr;
};
union TwoDimAbsBit
{
  struct
  {
    std::array<TwoDimAbsBoard, 96> two_dim_abs_board_arr;
    std::array<GdiRegU8, 17> abs_reg;
  };

  void toBigEndian()
  {
    for (auto& board : two_dim_abs_board_arr)
    {
      for (auto& comp : board.comp_arr)
      {
        for (auto& dist_area : comp.dist_area_arr)
        {
          dist_area.toBigEndian();
        }
      }
    }
  }

  std::array<char, static_cast<std::size_t>(48 * 1024 + 256)> arr;
};

constexpr std::array<uint16_t, 16> REFL_CALIB_DIST = { 40,   210,  400,  600,  1400, 2000,  3000,  4000,
                                                       5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000 };

struct AreaComp
{
  uint16_t refl_10;
  uint16_t refl_40;
  uint16_t refl_90;
  uint16_t refl_255;

  void toBigEndian();

  uint16_t& getReflChargeComp(const int _refl)
  {
    switch (_refl)
    {
    case 10: return refl_10;
    case 40: return refl_40;
    case 90: return refl_90;
    case 255: return refl_255;
    default: return refl_10;
    }
  }
};

struct ReflChargeComp
{
  std::array<AreaComp, REFL_CALIB_DIST.size()> dist_comp;

  void toBigEndian()
  {
    for (auto& dist : dist_comp)
    {
      dist.toBigEndian();
    }
  }
};

// 经验绝标
struct EmpAbsBit
{
  std::array<char, static_cast<std::size_t>(3 * 1024 + 256)> arr;
};

struct AbsCoeff
{
  uint16_t abs_coe_k;
  uint16_t abs_coe_b;
};

// 绝标
union AbsBit
{
  struct
  {
    std::array<std::array<AbsCoeff, 8>, 96> abs_coe_arr;
    std::array<GdiRegU8, 17> abs_reg;
  };
  std::array<char, static_cast<std::size_t>(3 * 1024 + 256)> arr;

  void toBigEndian();
};

struct ReflChnComp
{
  std::array<ReflChargeComp, 2> charge;
  void toBigEndian()
  {
    for (auto& comp : charge)
    {
      comp.toBigEndian();
    }
  }
};

// dist_index = [40 250 600 2000 4000 6000 8000 10000 12000 14000 16000 18000 20000 22000 24000 26000];

constexpr uint32_t REF_INDEX_REG_ADDR_START         = 0x8c2101;
constexpr std::array<uint8_t, 4> REFL_INDEX_VALUE   = { 10, 40, 90, 255 };
constexpr uint32_t DIST_INDEX_REG_ADDR_START        = 0x8c2105;
constexpr std::array<uint16_t, 16> DIST_INDEX_VALUE = { 40,   210,  400,  600,  1400, 2000,  3000,  4000,
                                                        5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000 };
constexpr uint32_t AREA_MIN_INDEX_REG_ADDR_START    = 0x8c2125;
constexpr uint16_t AREA_MIN_INDEX_VALUE             = 3250;

constexpr uint32_t PEAK_MIN_INDEX_REG_ADDR_START = 0x8c2127;
constexpr uint16_t PEAK_MIN_INDEX_VALUE          = 500;

constexpr uint32_t REF_CALIB_VERSION_REG_ADDR_START = 0x8c212d;

union ReflBit
{
  struct
  {
    std::array<ReflChnComp, 96> comp_chn;
    std::array<uint8_t, 73728> reserve;  // 96*1024-sizeof(comp_chn)
    std::array<GdiRegU8, REFL_INDEX_VALUE.size()> refl_index_reg;
    std::array<GdiRegU16, DIST_INDEX_VALUE.size()> dist_index_reg;
    GdiRegU16 area_min_index_reg;
    GdiRegU16 peak_min_index_reg;
  };

  std::array<char, static_cast<std::size_t>(97 * 1024)> arr;

  void toBigEndian()
  {
    for (auto& comp : comp_chn)
    {
      comp.toBigEndian();
    }
  }
};
union CombineBit
{
  struct
  {
    ReflBit refl_bit;
    uint16_t refl_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(ReflBit) - sizeof(uint16_t)> refl_reserve;

    DynamicBit dynamic_bit;
    uint16_t dynamic_crc;
    std::array<uint8_t, static_cast<size_t>(256 * 1024) - sizeof(DynamicBit) - sizeof(uint16_t)> dynamic_reserve;

    StaticBit static_bit;
    uint16_t static_crc;
    std::array<uint8_t, static_cast<size_t>(64 * 1024) - sizeof(StaticBit) - sizeof(uint16_t)> static_reserve;

    TwoDimAbsBit two_dim_abs_bit;
    uint16_t two_dim_abs_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(TwoDimAbsBit) - sizeof(uint16_t)> two_dim_abs_reserve;

    AbsBit abs_bit;
    uint16_t abs_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(AbsBit) - sizeof(uint16_t)> abs_reserve;
  };

  void toBigEndian()
  {
    refl_bit.toBigEndian();
    dynamic_bit.toBigEndian();
    static_bit.toBigEndian();
    abs_bit.toBigEndian();
  }

  [[nodiscard]] constexpr uint32_t getTwoAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }

  [[nodiscard]] uint32_t getReflOffset() const { return 0; }

  [[nodiscard]] uint32_t getDynamicOffset() const { return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size(); }

  [[nodiscard]] uint32_t getStaticOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size();
  }

  [[nodiscard]] uint32_t getTwoDimAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }

  [[nodiscard]] uint32_t getAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }
  std::array<char, static_cast<std::size_t>(704 * 1024)> arr = { 0 };
};

union CustomGdiReg
{
  struct
  {
    GdiRegU16 vbd_err;
    GdiRegU24 vbd_intercept;
    GdiRegU8 refl_calib_version_reg;
  };

  std::array<char, 1024> arr;
};

union CustomGdiFeild
{
  struct
  {
    CustomGdiReg custom_gdi_reg;
    uint16_t gdi_crc;
  };

  std::array<char, 65536> arr;
  [[nodiscard]] static uint32_t getStartAddr() { return 0xfb0000; }
};
#pragma pack(pop)

enum class DIDCmd : uint16_t
{
  PS_VER = 0xf195
};
enum class RIDCmd : uint16_t
{
  WRITE_REG = 0xf03a,
  READ_REG  = 0xf03b
};
enum class ServiceID : uint8_t
{
  READ_DID  = 0x22,
  WRITE_DID = 0x2e,
  RID_REQ   = 0x31,
  RID_RESP  = 0x71
};
struct DIDFrame
{
  ServiceID id;
  DIDCmd cmd;
};
struct RIDFrame
{
  ServiceID id;
  uint8_t sub_func;
  RIDCmd cmd;
};
}  // namespace mech
}  // namespace lidar
}  // namespace robosense
#endif  // MECH_DATA_STRUCT_H