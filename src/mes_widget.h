﻿/******************************************************************************
* Copyright 2020 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/
#ifndef MES_WIDGET_H
#define MES_WIDGET_H

#include "widget_log_setting.h"
#include <QObject>

using LogTestStatus = robosense::lidar::rsfsc_lib::LogTestStatus;

class MesWidget
{
private:
  MesWidget();

public:
  virtual ~MesWidget();
  static MesWidget* getInst();
  void releaseMESWidget();
  robosense::lidar::rsfsc_lib::WidgetLogSetting* getWidgetLogSettingPtr();
  void showWidget();

  bool checkMesIsOK(int _index, QString& _data_path, QString& _result_path, QString& _temp_path);
  void setVision(int _index, int _ps_vision, int _pl_vision);
  void finishProcess(const int _index,
                     const LogTestStatus& _log_status,
                     const QString& _fail_label = QString::fromUtf8(""),
                     const QString& _fail_msg   = QString::fromUtf8(""));

private:
  robosense::lidar::rsfsc_lib::WidgetLogSetting* widget_log_setting_ptr_ = nullptr;
};

#endif  // MES_WIDGET_H
