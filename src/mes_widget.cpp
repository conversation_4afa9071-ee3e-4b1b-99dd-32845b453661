﻿/******************************************************************************
* Copyright 2020 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/
#include "mes_widget.h"
#include "config.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QIcon>

MesWidget::MesWidget()
{
  widget_log_setting_ptr_ = new robosense::lidar::rsfsc_lib::WidgetLogSetting();
  widget_log_setting_ptr_->setWindowIcon(QIcon(":/img/icon.png"));
}

MesWidget::~MesWidget() { releaseMESWidget(); }

MesWidget* MesWidget::getInst()
{
  static MesWidget hw;
  return &hw;
}

void MesWidget::releaseMESWidget()
{
  if (widget_log_setting_ptr_ != nullptr)
  {
    if (widget_log_setting_ptr_->isVisible())
    {
      widget_log_setting_ptr_->close();
    }
    delete widget_log_setting_ptr_;
    widget_log_setting_ptr_ = nullptr;
  }
}

robosense::lidar::rsfsc_lib::WidgetLogSetting* MesWidget::getWidgetLogSettingPtr() { return widget_log_setting_ptr_; }

bool MesWidget::checkMesIsOK(int _index, QString& _data_path, QString& _result_path, QString& _temp_path)
{
  bool rtn = false;

  widget_log_setting_ptr_->addCheckSumDir(INSTALL_PREFIX_SHARE);

  switch (widget_log_setting_ptr_->checkAllState(_index, _data_path, _temp_path, _result_path))
  {
  case robosense::lidar::rsfsc_lib::CHECK_STATE_ITEM_EMPTY:
  {
    robosense::lidar::RSFSCLog::getInstance(_index)->error("MES参数设置不完整，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_PROCEDURE_ERROR:
  {
    robosense::lidar::RSFSCLog::getInstance(_index)->error("MES返回产品状态错误，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_PATH_NOT_SATISFY:
  {
    robosense::lidar::RSFSCLog::getInstance(_index)->error("Log配置路径失败，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_SUCCESS:
  {
    rtn = true;
    robosense::lidar::RSFSCLog::getInstance(_index)->info("检查MES状态成功");
    break;
  }
  default: rtn = false; break;
  }

  return rtn;
}

void MesWidget::setVision(int _index, int _ps_vision, int _pl_vision)
{
  widget_log_setting_ptr_->setFirmwareRevision(_index, _ps_vision, _pl_vision);
}

void MesWidget::finishProcess(const int _index,
                              const LogTestStatus& _log_status,
                              const QString& _fail_label,
                              const QString& _fail_msg)
{
  if (widget_log_setting_ptr_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance(_index)->error("widget_log_setting_ptr_ is nullptr");
    return;
  }

  widget_log_setting_ptr_->setTestStatus(_index, _log_status, _fail_label, _fail_msg);
  QString write_err;
  if (widget_log_setting_ptr_->finishProcess(_index, write_err))
  {
    if (!write_err.isEmpty())
    {
      robosense::lidar::RSFSCLog::getInstance(_index)->error(write_err.toStdString());
    }
  }
}

void MesWidget::showWidget()
{
  if (widget_log_setting_ptr_->isVisible())
  {
    widget_log_setting_ptr_->raise();
    widget_log_setting_ptr_->activateWindow();
  }
  else
  {
    widget_log_setting_ptr_->show();
  }
}
