/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "work_model/work_model_0351.h"
#include "app_event.h"
#include "common_struct.h"
#include "data_struct.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

bool WorkModel0351::humanConfirm()
{
  HumanConfirmResult human_confirm_result;
  app()->signalHumanConfirmResult(path_.verification_file_path, &human_confirm_result);
  std::unique_lock<std::mutex> lock(human_confirm_result.mutex);
  human_confirm_result.cv.wait(lock, [&human_confirm_result, this] { return isAbort() || human_confirm_result.done; });

  if (!addMeasureMessage("human_confirm", human_confirm_result.ret))
  {
    LOG_INDEX_ERROR("人工确认回读校验异常");
    return false;
  }
  return true;
}

bool WorkModel0351::writeVbdData()
{
  auto data_vec = generateVbdBit();
  if (data_vec.empty())
  {
    return false;
  }

  if (!getLidarManager()->writeTopFlashWithVer("write_vbd", data_vec, mech::CustomGdiFeild::getStartAddr()))
  {
    setFailMsg("写入VBD数据失败");
    addMeasureMessage("vbd_write_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("vbd_write_data", true);

  return true;
}
bool WorkModel0351::clearCalibData()
{
  constexpr uint32_t START_ADDR                      = 0xf00000;
  constexpr uint32_t END_ADDR                        = 0xffffff;
  std::array<char, (END_ADDR - START_ADDR + 1)> data = { 0 };

  std::vector<uint8_t> data_vec(data.begin(), data.end());
  if (!getLidarManager()->writeTopFlashWithVer("clear_calib_data", data_vec, START_ADDR))
  {
    setFailMsg("清除标定数据失败, all zero大小: {}kB", data.size() / 1024);
    addMeasureMessage("clear_calib_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("clear_calib_data", true);

  return true;
}

void WorkModel0351::setupAiryPldDifopCallback()
{
  // 重写limit CSV解析器为airy_pld_limit
  auto airy_pld_limit_csv_utils_ptr = app()->getCsvUtils("airy_pld_limit");
  if (airy_pld_limit_csv_utils_ptr != nullptr)
  {
    setLimitCsvUtils(airy_pld_limit_csv_utils_ptr);
    LOG_INDEX_INFO("已切换到airy_pld_limit CSV解析器");
  }
  else
  {
    LOG_INDEX_WARN("未找到airy_pld_limit CSV解析器，继续使用airy_limit");
  }

  // 重写原有的difop回调，将mech::DifopPacket转换为airy_pld::DifopPacket
  getLidarManager()->setReadDifopCallBack([this](const mech::DifopPacket& _difop_pkt) {
    // 将mech::DifopPacket的数据转换为airy_pld::DifopPacket
    // 注意：这里假设两个结构体的内存布局兼容，实际使用时需要根据具体情况调整
    std::memcpy(&last_airy_pld_difop_pkt_, &_difop_pkt,
                std::min(sizeof(airy_pld::DifopPacket), sizeof(mech::DifopPacket)));

    if (!last_airy_pld_difop_pkt_.isValid())
    {
      return;
    }
    last_airy_pld_difop_time_ = QDateTime::currentDateTime();
  });
}

airy_pld::DifopInfo WorkModel0351::getLastAiryPldDifopInfo() const
{
  return airy_pld::DifopInfo(last_airy_pld_difop_pkt_, last_airy_pld_difop_time_);
}

bool WorkModel0351::checkAgingData()
{
  // 使用airy_pld的difop数据进行检查
  if (getPastDifopTime() > para_info_.difop_timeout)
  {
    LOG_INDEX_ERROR("difop超时");
    addMeasureMessage("fsm_difop_timeout", getPastDifopTime(), rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    return false;
  }

  // 使用airy_pld的DifopInfo进行检查
  airy_pld::DifopInfo airy_pld_difop_info = getLastAiryPldDifopInfo();
  auto limit_csv_utils                    = getLimitCsvUtils();  // 使用当前设置的limit CSV解析器
  if (limit_csv_utils == nullptr)
  {
    LOG_INDEX_ERROR("无法获取limit CSV解析器");
    return false;
  }

  auto limit_name_vec = limit_csv_utils->getLimitNameVec();
  std::map<std::string, std::string> fail_msg;
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = limit_csv_utils->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    QVariant value      = airy_pld_difop_info.property(key.c_str());
    std::string name_zh = limit_info.extra_str_info.at(0);
    if (!checkWithLimit(limit_info, value))
    {
      fail_msg[key] =
        fmt::format("检测到异常值, 监控项: {}, value: {}, 下限: {}, 上限: {}", name_zh, dataToString(value),
                    dataToString(limit_info.min_th, value.type()), dataToString(limit_info.max_th, value.type()));
    }
  }

  if (!fail_msg.empty())
  {
    std::string error_msg = "老化数据检查失败:\n";
    for (const auto& [key, msg] : fail_msg)
    {
      error_msg += msg + "\n";
    }
    setFailMsg(error_msg);
    LOG_INDEX_ERROR("{}", error_msg);
    return false;
  }
  return true;
}

bool WorkModel0351::appendAgingData()
{
  airy_pld::DifopInfo airy_pld_difop_info = getLastAiryPldDifopInfo();
  last_airy_pld_difop_time_               = airy_pld_difop_info.getDateTime();

  auto limit_csv_utils = getLimitCsvUtils();  // 使用当前设置的limit CSV解析器
  if (limit_csv_utils == nullptr)
  {
    LOG_INDEX_ERROR("无法获取limit CSV解析器");
    return false;
  }

  auto limit_name_vec = limit_csv_utils->getLimitNameVec();

  QStringList state_list;
  QStringList value_list;

  value_list.append(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
  state_list.append("");
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = limit_csv_utils->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    QVariant value = airy_pld_difop_info.property(key.c_str());
    value_list.append(dataToString(value));
    state_list.append(checkWithLimit(limit_info, value) ? "PASS" : "FAIL");
  }

  // 添加光通误码率数据
  auto up_err_rate_limit_info   = limit_csv_utils->getLimitInfo("fsm_up_error_rate");
  auto down_err_rate_limit_info = limit_csv_utils->getLimitInfo("fsm_down_error_rate");
  double up_error_rate          = NAN;
  double down_error_rate        = NAN;
  getOpticalErrorRate(up_error_rate, down_error_rate);
  value_list.append(QString::number(up_error_rate));
  value_list.append(QString::number(down_error_rate));
  state_list.append(checkWithLimit(up_err_rate_limit_info, up_error_rate) ? "PASS" : "FAIL");
  state_list.append(checkWithLimit(down_err_rate_limit_info, down_error_rate) ? "PASS" : "FAIL");

  // 使用与基类相同的文件写入方式
  QFile file(path_.data_dir.absoluteFilePath("aging_data.csv"));
  QTextStream stream(&file);

  // 检查文件是否存在
  if (!file.exists())
  {
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
      LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
      return false;
    }

    // 写入初始信息
    QString lidar_sn      = getLidarManager()->getLidarInfoSn();
    QString aging_version = app()->getVersionStr();
    QString bot_version   = QString("0x%1").arg(QString::number(airy_pld_difop_info.getBotFirmwareVersion(), 16));
    QString top_version   = QString("0x%1").arg(QString::number(airy_pld_difop_info.getTopFirmwareVersion(), 16));
    QString motor_version = QString("0x%1").arg(QString::number(airy_pld_difop_info.getMotorFirmwareVersion(), 16));

    stream << "Lidar SN," << lidar_sn << "\r\n";
    stream << "AGING_VERSION," << aging_version << "\r\n";
    stream << "BOT_VERSION," << bot_version << "\r\n";
    stream << "TOP_VERSION," << top_version << "\r\n";
    stream << "MOTOR_VERSION," << motor_version << "\r\n";

    QStringList limit_title = { "NO", "Item", "Unit", "USL", "LSL" };
    stream << limit_title.join(",") << "\r\n";

    int count = 1;
    QStringList limit_str_list;
    for (const auto& key : limit_name_vec)
    {
      auto limit_info = limit_csv_utils->getLimitInfo(key);
      if (limit_info.extra_str_info.size() < 2)
      {
        continue;
      }
      if (limit_info.extra_str_info.at(1) != "difop")
      {
        continue;
      }
      QVariant value = airy_pld_difop_info.property(key.c_str());
      QString min_th = QString::number(limit_info.min_th);
      QString max_th = QString::number(limit_info.max_th);
      if (static_cast<QMetaType::Type>(value.type()) == QMetaType::UInt)
      {
        if (min_th != "nan")
        {
          min_th = QString::fromStdString(fmt::format("{:#x}", static_cast<uint32_t>(limit_info.min_th)));
        }
        if (max_th != "nan")
        {
          max_th = QString::fromStdString(fmt::format("{:#x}", static_cast<uint32_t>(limit_info.max_th)));
        }
      }
      QStringList limit_data = { QString::number(count++), QString::fromStdString(key),
                                 QString::fromStdString(limit_info.getUnit()), max_th, min_th };
      stream << limit_data.join(",") << "\r\n";
      limit_str_list.append(QString::fromStdString(key));
    }
    limit_str_list.append("up_error_rate");
    limit_str_list.append("down_error_rate");

    stream << "\r\n";
    stream << "datetime," << limit_str_list.join(",") << "\r\n";
    stream << "\r\n";
    file.close();
  }

  // 处理已有文件：读取内容，去除最后一行
  if (!file.open(QIODevice::ReadWrite | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
    return false;
  }

  QStringList file_content;
  QTextStream inp(&file);
  while (!inp.atEnd())
  {
    QString line = inp.readLine();
    file_content.append(line);
  }

  // 移除最后一行
  if (!file_content.isEmpty())
  {
    file_content.removeLast();
  }

  file.resize(0);                                 // 清空文件内容以便写入
  stream << file_content.join("\r\n") << "\r\n";  // 重新写入文件内容

  // 写入新数据
  stream << value_list.join(",") << "\r\n";
  stream << state_list.join(",") << "\r\n";

  return true;
}

}  // namespace lidar
}  // namespace robosense