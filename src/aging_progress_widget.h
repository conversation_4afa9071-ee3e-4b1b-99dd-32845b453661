﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef AGING_PROGRESS_WIDGET_H
#define AGING_PROGRESS_WIDGET_H

#include "lidar_ctl.h"
#include "rsfsc_fsm/finite_state_machine.h"
#include "running_widget.h"
#include <QtWidgets>
#include <memory>
#include <qpixmap.h>

namespace robosense
{

namespace lidar
{
class AgingProgressWidget : public QWidget
{
  Q_OBJECT
public:
  explicit AgingProgressWidget(const ParaInfo& _para_info, QWidget* _parent = nullptr);
  ~AgingProgressWidget() override;

public:
  void setLidarSn(QString _sn);
  void setLidarProject(QString _project);
  void setAgingProgress(int _counts);
  void quit();
  std::shared_ptr<AgingWorkModel> getWorkModelPtr() { return work_model_ptr_; }

  void setParaInfo(const ParaInfo& _para_info);

  void setRelayState(ConnectionStatus _state);
  void updateRelayState();
  QString getLidarSn();
  void resetState();

  void initStateHandler();

  [[nodiscard]] int getLidarIndex() const;
  [[nodiscard]] int getLogIndex() const { return getLidarIndex(); }

  void setOperateEnabled(bool _enabled);

public Q_SLOTS:
  void slotShowLidarInfoWidget();
  void slotShowAgingData();
  void slotStartAging(bool _is_start);
  void slotAgingTimer();
  void slotSwitchFirmwareUpdateState(const FirmwareUpdateState _state);
  void slotSwitchCodeWheelCalibState(const EncodeCalibState _state);
  void slotSwitchAgingState(const AgingState _state);
  void slotSwitchStressState(const StressState _state);
  void slotSwitchRunState(const RunState _state);
  void slotShowAgingLog();
  void slotUpdateStressCount(int _count);
  void slotStartStressTest();
  void slotExit();
  void slotAgingTimerStop();

private:
  static QString secsToTime(int _secs);
  void initProgress();
  void initFsm();

private:
  QLabel* label_index_         = nullptr;
  QLabel* label_sn_            = nullptr;
  QLabel* label_project_       = nullptr;
  QLabel* label_stress_state_  = nullptr;
  QLabel* label_stress_counts_ = nullptr;
  QLabel* label_relay_state_   = nullptr;
  QLabel* label_run_state_     = nullptr;

  QPushButton* button_firmware_update_  = nullptr;
  QPushButton* button_code_wheel_calib_ = nullptr;
  QPushButton* button_aging_state_      = nullptr;
  QPushButton* button_stress_state_     = nullptr;
  QProgressBar* progress_aging_         = nullptr;
  QLabel* label_aging_pass_time_        = nullptr;
  QDialog* dialog_aging_data_           = nullptr;
  RunningWidget* running_widget_        = nullptr;
  QPixmap pixmap_uninitialized_;
  QPixmap pixmap_connected_;
  QPixmap pixmap_disconnected_;
  QPixmap pixmap_lagging_;

  mech::DifopPacket last_difop_pkt_;
  int aging_data_pass_time_ = 0;

  QTimer* aging_timer_ = nullptr;

  int lidar_index_ = 0;
  ConnectionStatus relay_state_;

  int total_time_secs_      = 0;
  int aging_time_secs_      = 0;
  int stress_time_secs_     = 0;
  int curr_time_secs_       = 0;
  int last_time_check_secs_ = 0;
  int stress_counts_        = 0;

private:
  ParaInfo para_info_;
  QSharedPointer<QSet<QString>> aging_sn_set_ptr_ = nullptr;
  std::shared_ptr<FiniteStateMachine<AgingWorkModel>> fsm_ptr_;
  std::shared_ptr<AgingWorkModel> work_model_ptr_;
};

}  // namespace lidar

}  // namespace robosense
#endif  // AGING_PROGRESS_WIDGET_H
