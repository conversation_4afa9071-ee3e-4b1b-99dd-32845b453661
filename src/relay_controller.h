﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RELAY_CONTROLLER_H
#define RELAY_CONTROLLER_H

#include "relay_controller_driver/relay_controller_factory.h"
#include <QEventLoop>
#include <QObject>
#include <QVariant>
#include <boost/asio/detail/chrono.hpp>
#include <memory>
#include <mutex>

const int G_MAX_RELAY_NUM = 6;

enum RelayLayout
{
  RELAY_LAYOUT0_HHL,
  RELAY_LAYOUT0_SS,
  RELAY_LAYOUT0_NORMAL,
  RELAY_LAYOUT0_UNKNOW,
};

namespace robosense
{
namespace lidar
{

struct OperationContext
{
public:
  QEventLoop event_loop;
  std::mutex mutex;
  std::unique_lock<std::mutex> lock { mutex };
  bool success = false;  // 操作结果是否成功
  QVariant result;       // 操作结果

  OperationContext()
  {
    QMetaObject::invokeMethod(&event_loop, [&] { lock.unlock(); });
  }

  // 参考条件变量的wait，结束等待的时候需要重新获取回锁，避免发生竞态问题
  bool wait()
  {
    event_loop.exec();
    lock.lock();
    return success;
  }
};

// 在析构的时候自动notify，并解锁，使用与lock_guard一样的机制来保证在析构的时候自动解锁，自动notify
class ContextGuard
{
public:
  ContextGuard(const ContextGuard&) = delete;
  ContextGuard(ContextGuard&&)      = delete;
  ContextGuard& operator=(const ContextGuard&) = delete;
  ContextGuard& operator=(ContextGuard&&) = delete;
  explicit ContextGuard(OperationContext* _context) : context_ptr_(_context), lock_(_context->mutex) {}
  ~ContextGuard() { context_ptr_->event_loop.quit(); }

private:
  OperationContext* context_ptr_;
  std::unique_lock<std::mutex> lock_;
};

class RelayController : public QObject
{
  Q_OBJECT
private:
  explicit RelayController(QObject* _parent = nullptr);
  ~RelayController() override;

public:
  static RelayController* getInstance();

Q_SIGNALS:
  void signalRelayTurn(int _lidar_index, bool _is_turn_on, OperationContext* _context);

public Q_SLOTS:
  void slotRelayTurn(int _lidar_index, bool _is_turn_on, OperationContext* _context);

public:
  bool initRelayController(const RelayLayout& _relay_layout, const std::vector<std::string>& _port_name);
  bool relayTurnPrivate(int _lidar_index, bool _is_turn_on);  //_index=0xff,turn all channel
  bool relayTurn(int _lidar_index, bool _is_turn_on);

private:
  bool getRelayIndexMems(int _lidar_index, int& _relay_module_index, int& _relay_module_channel);
  bool getRelayIndexHelios(int _lidar_index, int& _relay_module_index, int& _relay_module_channel);

private:
  std::array<std::unique_ptr<robosense::lidar::RelayControllerInterface>, G_MAX_RELAY_NUM> relay_controller_driver_;
  std::mutex mutex_;
  RelayLayout relay_layout_ = RELAY_LAYOUT0_UNKNOW;
  std::chrono::time_point<std::chrono::system_clock> last_time_;
};
}  // namespace lidar
}  // namespace robosense

#endif  // RELAY_CONTROLLER_H
