﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "tricolor_light_controller.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <memory>
#include <vector>

namespace robosense
{
namespace lidar
{
TricolorLightController::TricolorLightController()
{
  // 定时器初始化，定时间隔为1s
  time = std::make_unique<QTimer>(new QTimer);
  time->setInterval(1000);
  QObject::connect(time.get(), &QTimer::timeout, this, &TricolorLightController::slotOnTimeOut);
}

TricolorLightController::~TricolorLightController()
{
  turnOffController(TriLightAndBeep::RED_LIGHT);
  turnOffController(TriLightAndBeep::YELLOW_LIGHT);
  turnOffController(TriLightAndBeep::GREEN_LIGHT);
  turnOffController(TriLightAndBeep::BEEP);
  //std::this_thread::sleep_for(std::chrono::microseconds(2000));
  time->stop();
  if (time)
  {
    time.reset();
  }
}

TricolorLightController* TricolorLightController::getInstance()
{
  static TricolorLightController hw;
  return &hw;
}

bool TricolorLightController::init(const std::string& _port_name,
                                   int _aging_num,
                                   int _red_index,
                                   int _yellow_index,
                                   int _green_index,
                                   int _beep_index)
{

  for (int i = 0; i < _aging_num; i++)
  {
    aging_flag_map_[i + 1] = (RUN_IDLE);
  }

  if (_red_index < 0 || _yellow_index < 0 || _green_index < 0 || _beep_index < 0)
  {
    return false;
  }

  light_map_[RED_LIGHT]    = _red_index;
  light_map_[YELLOW_LIGHT] = _yellow_index;
  light_map_[GREEN_LIGHT]  = _green_index;
  light_map_[BEEP]         = _beep_index;

  tri_color_controller_driver_.reset(
    RelayControllerFactory::createRelayController(RelayControllerFactory::ZHONG_KAI_CONTROLLER_16, { _port_name }));
  if (tri_color_controller_driver_ == nullptr)
  {
    RSFSCLog::getInstance()->error("三色灯继电器初始化{}失败", _port_name);
    return false;
  }
  std::string err_msg;
  if (!tri_color_controller_driver_->connectController(err_msg))
  {
    RSFSCLog::getInstance()->error("三色灯继电器连接失败:{}, port name: {}", err_msg, _port_name);
    return false;
  }

  switchLightState();

  is_init_ = true;
  return true;
}

bool TricolorLightController::getIsInit() { return is_init_; }

void TricolorLightController::setAgingState(int _lidar_index, RunState _run_state)
{
  if (!is_init_ || is_disable_)
  {
    return;
  }

  if (aging_flag_map_.find(_lidar_index) == aging_flag_map_.end())
  {
    return;
  }

  aging_flag_map_[_lidar_index] = _run_state;
  run_state_                    = _run_state;
  switchLightState();
}

bool TricolorLightController::turnOnController(robosense::lidar::TriLightAndBeep _type, unsigned int _frequency)
{
  bool rtn = true;
  if (!is_init_ || is_disable_)
  {
    return false;
  }
  std::string msg_err;
  if (light_map_.at(_type) <= 0)
  {
    return true;
  }
  int index = light_map_.at(_type);

  rtn = tri_color_controller_driver_->turnOnChannel(index, msg_err);
  if (!rtn)
  {
    robosense::lidar::RSFSCLog::getInstance()->debug("三色灯继电器操作失败:{0}", msg_err);
  }

  robosense::lidar::RSFSCLog::getInstance()->debug("三色灯打开:{0}", G_TRI_LIGHT_AND_BEEP_STR.at(_type));
  return rtn;
}

bool TricolorLightController::turnOffController(robosense::lidar::TriLightAndBeep _type)
{
  bool rtn = true;
  if (!is_init_)
  {
    return false;
  }
  std::string msg_err;
  if (light_map_.at(_type) <= 0)
  {
    return true;
  }
  int index = light_map_.at(_type);

  rtn = tri_color_controller_driver_->turnOffChannel(index, msg_err);
  if (!rtn)
  {
    robosense::lidar::RSFSCLog::getInstance()->debug("三色灯继电器操作失败:{0}", msg_err);
  }

  robosense::lidar::RSFSCLog::getInstance()->debug("三色灯关闭:{0}", G_TRI_LIGHT_AND_BEEP_STR.at(_type));
  return rtn;
}

void TricolorLightController::switchLightState()
{
  int busy_num = 0;
  int ng_num   = 0;
  for (auto [index, run_state] : aging_flag_map_)
  {
    switch (run_state)
    {
    case RUN_IDLE: break;
    case RUN_BUSY: busy_num++; break;
    case RUN_PASS: break;
    case RUN_NG: ng_num++; break;
    case RUN_ABORT: break;
    }
  }

  if (ng_num > 0)
  {
    turnOnController(RED_LIGHT);
    turnOnController(BEEP);
  }
  else
  {
    turnOffController(RED_LIGHT);
    turnOffController(BEEP);
  }

  if (run_state_ == RUN_PASS)
  {
    startToggleLight(GREEN_LIGHT, true, 10);
  }

  if (busy_num > 0)
  {
    turnOnController(YELLOW_LIGHT);
  }
  else
  {
    turnOffController(YELLOW_LIGHT);
  }
}

void TricolorLightController::startToggleLight(TriLightAndBeep _type, const bool _final, const int _duration)
{
  QTimer* timer = new QTimer;
  timer->setInterval(1000);  // 设置 1 秒的时间间隔

  // 计数器，记录已经经过的秒数
  int elapsed_seconds = 0;
  bool is_light_on    = false;

  QObject::connect(timer, &QTimer::timeout, [=, &elapsed_seconds, &is_light_on]() mutable {
    // Toggle 操作，每次切换灯光状态
    if (is_light_on)
    {
      turnOffController(_type);
    }
    else
    {
      turnOnController(_type);
    }
    is_light_on = !is_light_on;

    elapsed_seconds++;

    // 检查是否已经达到持续时间
    if (elapsed_seconds >= _duration)
    {
      timer->stop();
      timer->deleteLater();  // 停止定时器并删除

      // 如果 _final 为 true，确保最后的状态保持开启
      if (_final && !is_light_on)
      {
        turnOnController(_type);
      }
    }
  });

  timer->start();  // 开始定时器
}

void TricolorLightController::slotOnTimeOut()
{
  static bool is_open = false;
  switch (run_state_)
  {
  case RUN_NG: is_open ? turnOnController(RED_LIGHT) : turnOffController(RED_LIGHT); break;

  case RUN_PASS: is_open ? turnOnController(GREEN_LIGHT) : turnOffController(GREEN_LIGHT); break;

  default: time->stop(); break;
  }

  is_open = !is_open;
}

}  // namespace lidar
}  // namespace robosense
