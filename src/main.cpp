﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "config.h"
#include "main_window.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "widget_log_setting.h"

#include <cmath>
#include <csignal>
#include <string>

#include <QApplication>

void sigHandle(int _sig)
{
  QApplication::exit();  //会调用~MainWindow()
}

int main(int _argc, char** _argv)
{
  robosense::lidar::rsfsc_lib::WidgetLogSetting::init(
    _argc, _argv, true ? "" : PROJECT_NAME, PROJECT_NAME, "201", FRAMEWORK_TEMPLATE_VERSION_MAJOR,
    FRAMEWORK_TEMPLATE_VERSION_MINOR, FRAMEWORK_TEMPLATE_VERSION_PATCH, FRAMEWORK_TEMPLATE_VERSION_TWEAK,
    std::string(CMAKE_BUILD_TYPE) == "Debug", robosense::lidar::rsfsc_lib::CableManageStatus::DISABLE);

  QApplication app(_argc, _argv);
  Q_INIT_RESOURCE(resource);  // https://doc.qt.io/qt-5/resources.html
  signal(SIGINT, sigHandle);

  robosense::lidar::MainWindow w(_argc, _argv);
  w.show();
  app.connect(&app, &QApplication::lastWindowClosed, &app, &QApplication::quit);
  return app.exec();
}
