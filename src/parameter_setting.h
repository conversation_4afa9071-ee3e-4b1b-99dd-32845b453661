﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef PARAMETER_SETTING_H
#define PARAMETER_SETTING_H

#include <QEvent>
#include <QObject>
#include <QTreeWidget>
#include <QtWidgets>
#include <memory>

enum ParaType
{
  PARA_INT,
  PARA_DOUBLE,
  PARA_STRING,
  PARA_OPTION,
  PARA_BOOL,
  PARA_PATH,
};

struct ParaInt
{
  int para_default   = 0;  //参数默认值
  int para_min       = 0;  //参数最小值
  int para_max       = 0;  //参数最大值
  int para_cur_value = 0;  //当前数值
  int step           = 1;
};

struct ParaDouble
{
  double para_default   = 0;  //参数默认值
  double para_min       = 0;  //参数最小值
  double para_max       = 0;  //参数最大值
  double decimal_num    = 3;  //小数点后位数
  double para_cur_value = 0;  //当前数值
  double step           = 0.1;
};

struct ParaString
{
  std::string para_default   = "";  //参数默认值
  std::string para_cur_value = "";  //当前数值
  bool is_password           = false;
};

struct ParaOption
{
  int para_default = 0;    //参数默认值
  QStringList para_list;   //参数列表
  int para_cur_value = 0;  //当前数值
};

struct ParaBool
{
  bool para_default   = false;  //参数默认值
  bool para_cur_value = false;  //当前数值
};

struct ParaPath
{
  std::string para_default   = "";  //参数默认值
  std::string para_cur_value = "";  //当前数值
  bool is_folder             = false;
};

class ParaProperty
{
public:
  std::string key_name      = "";  //分组的名称
  std::string para_name     = "";  //参数的名称
  std::string para_describe = "";  //描述
  ParaType para_type;              //参数类型

  QVariant para;
};

Q_DECLARE_METATYPE(ParaInt)
Q_DECLARE_METATYPE(ParaDouble)
Q_DECLARE_METATYPE(ParaString)
Q_DECLARE_METATYPE(ParaOption)
Q_DECLARE_METATYPE(ParaBool)
Q_DECLARE_METATYPE(ParaPath)
Q_DECLARE_METATYPE(std::string)

std::list<std::string> splitString(std::string _src_str, std::string _delim_str, bool _repeated_char_ignored);
class ParaTreeWidget;
class PathSelectControl;
class ParameterSetting : public QWidget
{
  Q_OBJECT
public:
  ParameterSetting(QWidget* _parent = nullptr);
  virtual ~ParameterSetting();

public:
  void loadPara(std::string _path);

  //每次运行必须注册参数,才知道当前需要的哪些参数,如果不注册,之前存在后台的参数将会被删除
  void registerIntPara(std ::string _para_key,   //分组的名称
                       std ::string _para_name,  //参数的名称
                       int _para_min,            //参数最小值
                       int _para_max,            //参数最大值
                       int _default_value,       //参数默认值
                       int _step,
                       std ::string _para_describe = "");

  void registerDoublePara(std ::string _para_key,   //分组的名称
                          std ::string _para_name,  //参数的名称
                          double _para_min,         //参数最小值
                          double _para_max,         //参数最大值
                          double _default_value,    //参数默认值
                          int _decimal_num,         //小数点后位数
                          double _step,
                          std ::string _para_describe = "");

  void registerStringPara(std ::string _para_key,       //分组的名称
                          std ::string _para_name,      //参数的名称
                          std ::string _default_value,  //参数默认值
                          std ::string _para_describe = "",
                          bool _is_password           = false);

  void registerOptionPara(std ::string _para_key,             //分组的名称
                          std ::string _para_name,            //参数的名称
                          std::list<std::string> _para_list,  //参数列表
                          int _default_index,                 //参数默认值
                          std ::string _para_describe = "");

  void registerBoolPara(std ::string _para_key,   //分组的名称
                        std ::string _para_name,  //参数的名称
                        bool _default_value,      //参数默认值
                        std ::string _para_describe = "");

  void registerPathPara(std ::string _para_key,       //分组的名称
                        std ::string _para_name,      //参数的名称
                        std ::string _default_value,  //参数默认值
                        bool _is_folder,              //true=文件夹,false=文件
                        std ::string _para_describe = "");

  bool changePara(std::string _para_key, std::string _para_name, QVariant _change_value);

  bool getPara(std::string _para_key, std::string _para_name, int& _value);
  bool getPara(std::string _para_key, std::string _para_name, double& _value);
  bool getPara(std::string _para_key, std::string _para_name, std::string& _value);
  bool getPara(std::string _para_key, std::string _para_name, QString& _value);
  bool getPara(std::string _para_key, std::string _para_name, bool& _value);

  std::map<std::string, ParaProperty> getAllParaProperty();

  void showParaTree();

private:
  void loadPara();
  void savePara();

private Q_SLOTS:
  void slotSpinBoxChange(std::string _key, int _value);
  void slotSpinDoubleBoxChange(std::string _key, double _value);
  void slotLineEditChange(std::string _key);
  void slotComboBoxChange(std::string _key, int _value);
  void slotCheckBoxChange(std::string _key, bool _value);
  void slotPathControlChange(std::string _key);

private:
  std::unique_ptr<ParaTreeWidget> para_tree_;
  std::string para_path_;
  std::map<std::string, ParaProperty> para_map_;
  std::list<std::string> para_index_list_;
};

class ParaTreeWidget : public QTreeWidget
{
public:
  ParaTreeWidget(QWidget* _parent = nullptr) : QTreeWidget(_parent)
  {
    //安装事件过滤器
    Q_FOREACH (QAbstractSpinBox* sb, this->findChildren<QAbstractSpinBox*>())
    {
      sb->installEventFilter(this);
    }

    Q_FOREACH (QComboBox* cb, this->findChildren<QComboBox*>())
    {
      cb->installEventFilter(this);
    }
  }
  virtual ~ParaTreeWidget() {};

protected:
  virtual bool eventFilter(QObject* _obj, QEvent* _event) override
  {
    //屏蔽 spinbox 和 combobox 的滚轮事件
    if (_obj->inherits("QAbstractSpinBox") || _obj->inherits("QComboBox"))
    {
      if (_event->type() == QEvent::Wheel)
        return true;
    }

    return QTreeWidget::eventFilter(_obj, _event);
  }
};

class PathSelectControl : public QWidget
{
  Q_OBJECT
public:
  PathSelectControl(bool _is_folder, QWidget* _parent = nullptr);
  virtual ~PathSelectControl();

public:
  void selectPath();
  std::string getCurrentPath() { return current_path_; }
  void setCurrentPath(std::string _path);

private Q_SLOTS:
  void slotSelectPath();

Q_SIGNALS:
  void signalSelectFinish();

private:
  std::string current_path_ = "";
  QLineEdit* path_edit_     = nullptr;
  QPushButton* sel_button_  = nullptr;
  bool is_folder_           = false;
};

extern std::list<std::string> splitString(std::string _src_str, std::string _delim_str, bool _repeated_char_ignored);

#endif  // PARAMETER_SETTING_H
