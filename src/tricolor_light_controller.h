﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef TRICOLOR_LIGHT_CONTROLLER
#define TRICOLOR_LIGHT_CONTROLLER

#include "data_struct.h"
#include "relay_controller.h"
#include <QObject>
#include <QTimer>
#include <array>
#include <memory>
#include <string>
#include <vector>

// 空闲: 不亮灯
// 有忙碌: 黄灯
// 有pass:绿灯闪
// 有NG:红灯+蜂鸣
namespace robosense
{
namespace lidar
{
constexpr int RED_LIGHT_CHANNEL    = 1;
constexpr int YELLOW_LIGHT_CHANNEL = 2;
constexpr int GREEN_LIGHT_CHANNEL  = 3;
constexpr int BEEP_CHANNEL         = 4;

enum TriLightAndBeep
{
  RED_LIGHT    = 0,
  YELLOW_LIGHT = 1,
  GREEN_LIGHT  = 2,
  BEEP         = 3
};

const std::vector<std::string> G_TRI_LIGHT_AND_BEEP_STR({ "红灯", "黄灯", "绿灯", "蜂鸣器" });

enum WorkingState
{
  TEST_WORKING = 0,
  TEST_SUCCESSFUL,
  TEST_FAILED,
  NO_TEST,
  TEST_INIT,
  NONE
};

class TricolorLightController : public QObject
{
  Q_OBJECT
private:
  explicit TricolorLightController();
  ~TricolorLightController();

public:
  static TricolorLightController* getInstance();

public:
  bool init(const std::string& _port_name,
            int _aging_num,
            int _red_index,
            int _yellow_index,
            int _green_index,
            int _beep_index);
  bool getIsInit();
  void setAgingState(int _lidar_index, RunState _aging_state);
  std::unique_ptr<QTimer> time;  //定时器
  std::shared_ptr<RelayControllerInterface> getController() { return tri_color_controller_driver_; }
  void setDisable(bool _is_disable) { is_disable_ = _is_disable; }

private:
  bool turnOnController(TriLightAndBeep _type, unsigned int _frequency = 0);
  bool turnOffController(TriLightAndBeep _type);
  void switchLightState();

  void startToggleLight(TriLightAndBeep _type, const bool _final, const int _duration = 10);

protected Q_SLOTS:
  void slotOnTimeOut();

private:
  bool is_disable_ = false;
  std::map<int, RunState> aging_flag_map_;
  RelayController* relay_controller_ = nullptr;
  bool is_init_;  //继电器初始化是否成功的标志位
  RunState run_state_ = RUN_IDLE;
  std::map<int, int> light_map_;
  std::shared_ptr<robosense::lidar::RelayControllerInterface> tri_color_controller_driver_;
};

}  // namespace lidar
}  // namespace robosense

#endif  //_TRICOLOR_LIGHT_CONTROLLER
