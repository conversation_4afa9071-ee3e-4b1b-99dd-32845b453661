﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "parameter_setting.h"
#include "config.h"
#include "rsfsc_qsettings.h"
#include <QFileDialog>
#include <QtCore/QTextCodec>
#include <iostream>
#include <utility>

ParameterSetting::ParameterSetting(QWidget* _parent)
{
  this->setParent(_parent);

  this->setWindowFlags(Qt::WindowStaysOnTopHint);
  //this->setWindowModality(Qt::WindowModal);  //阻挡父亲窗口内其他控件，除非本dialog关闭
  this->setAttribute(Qt::WA_ShowModal, true);

  para_tree_ = std::make_unique<ParaTreeWidget>(this);

  para_tree_->setColumnCount(2);

  para_tree_->header()->setSectionResizeMode(QHeaderView::ResizeToContents);  //自适应列宽
  para_tree_->header()->setDefaultAlignment(Qt::AlignCenter);                 //表头居中
  para_tree_->setHeaderLabels({ "名称", "参数值" });                          //表头名称
  para_tree_->setStyleSheet(("QTreeWidget::item {border:1px solid #E8E8E8}"
                             "QTreeWidget::item::selected {background-color:#4682B4}"));

  QHBoxLayout* layout = new QHBoxLayout;
  layout->addWidget(para_tree_.get());
  this->setLayout(layout);
}

ParameterSetting::~ParameterSetting() {}

void ParameterSetting::loadPara(std::string _path)
{
  para_path_.append(PROJECT_NAME).append("/").append(_path);
  loadPara();
}

void ParameterSetting::registerIntPara(std::string _para_key,
                                       std::string _para_name,
                                       int _para_min,
                                       int _para_max,
                                       int _default_value,
                                       int _step,
                                       std ::string _para_describe)
{
  if (_para_min > _para_max)
  {
    qDebug() << "参数注册失败,参数的上限应该高于下限";
    return;
  }

  ParaInt para;
  para.para_min       = _para_min;
  para.para_max       = _para_max;
  para.para_cur_value = para.para_default = _default_value;
  para.step                               = _step;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_INT;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

void ParameterSetting::registerDoublePara(std::string _para_key,
                                          std::string _para_name,
                                          double _para_min,
                                          double _para_max,
                                          double _default_value,
                                          int _decimal_num,
                                          double _step,
                                          std ::string _para_describe)
{
  if (_para_min > _para_max)
  {
    qDebug() << "参数注册失败,参数的上限应该高于下限";
    return;
  }

  ParaDouble para;
  para.para_min       = _para_min;
  para.para_max       = _para_max;
  para.para_cur_value = para.para_default = _default_value;
  para.decimal_num                        = _decimal_num;
  para.step                               = _step;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_DOUBLE;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

void ParameterSetting::registerStringPara(std::string _para_key,
                                          std::string _para_name,
                                          std::string _default_value,
                                          std ::string _para_describe,
                                          bool _is_password)
{
  ParaString para;
  para.para_cur_value = para.para_default = _default_value;
  para.is_password                        = _is_password;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_STRING;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

void ParameterSetting::registerOptionPara(std::string _para_key,
                                          std::string _para_name,
                                          std::list<std::string> _para_list,
                                          int _default_index,
                                          std ::string _para_describe)
{
  ParaOption para;
  for (auto it : _para_list)
  {
    para.para_list.append(it.data());
  }
  para.para_cur_value = para.para_default = _default_index;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_OPTION;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

void ParameterSetting::registerBoolPara(std::string _para_key,
                                        std::string _para_name,
                                        bool _default_value,
                                        std ::string _para_describe)
{
  ParaBool para;
  para.para_cur_value = para.para_default = _default_value;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_BOOL;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

void ParameterSetting::registerPathPara(std::string _para_key,
                                        std::string _para_name,
                                        std::string _default_value,
                                        bool _is_folder,
                                        std::string _para_describe)
{
  ParaPath para;
  para.para_cur_value = para.para_default = _default_value;
  para.is_folder                          = _is_folder;

  ParaProperty para_property;
  para_property.key_name      = _para_key;
  para_property.para_name     = _para_name;
  para_property.para_describe = _para_describe.length() > 0 ? _para_describe : _para_name;
  para_property.para.setValue(para);
  para_property.para_type = PARA_PATH;

  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  para_map_[key] = para_property;
  para_index_list_.push_back(key);
}

bool ParameterSetting::changePara(std::string _para_key, std::string _para_name, QVariant _change_value)
{
  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  std::map<std::string, ParaProperty>::iterator it = para_map_.find(key);
  if (it == para_map_.end())
  {
    qDebug() << "para no find" << key.data();
    return false;
  }

  ParaType para_type = it->second.para_type;
  switch (para_type)
  {
  case PARA_INT:
  {
    ParaInt para        = it->second.para.value<ParaInt>();
    para.para_cur_value = _change_value.value<int>();
    it->second.para.setValue(para);
  }
  break;
  case PARA_DOUBLE:
  {
    ParaDouble para     = it->second.para.value<ParaDouble>();
    para.para_cur_value = _change_value.value<double>();
    it->second.para.setValue(para);
  };
  break;
  case PARA_STRING:
  {
    ParaString para     = it->second.para.value<ParaString>();
    para.para_cur_value = _change_value.value<QString>().toStdString();
    it->second.para.setValue(para);
  };
  break;
  case PARA_OPTION:
  {
    ParaOption para     = it->second.para.value<ParaOption>();
    para.para_cur_value = _change_value.value<int>();
    it->second.para.setValue(para);
  };
  break;
  case PARA_BOOL:
  {
    ParaBool para       = it->second.para.value<ParaBool>();
    para.para_cur_value = _change_value.value<bool>();
    it->second.para.setValue(para);
  };
  break;
  case PARA_PATH:
  {
    ParaPath para       = it->second.para.value<ParaPath>();
    para.para_cur_value = _change_value.value<QString>().toStdString();
    it->second.para.setValue(para);
  };
  break;
  }

  std::string para_path = para_path_;
  robosense::lidar::rsfsc_lib::RSFSCQSettings setting("RoboSense", para_path.data());
  setting.setIniCodec(QTextCodec::codecForName("UTF_8"));
  setting.setValue(key.data(), _change_value);
  return true;
}

bool ParameterSetting::getPara(std::string _para_key, std::string _para_name, int& _value)
{
  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  std::map<std::string, ParaProperty>::iterator it = para_map_.find(key);
  if (it == para_map_.end())
  {
    return false;
  }

  ParaType para_type = it->second.para_type;
  switch (para_type)
  {
  case PARA_INT: _value = it->second.para.value<ParaInt>().para_cur_value; break;
  case PARA_OPTION: _value = it->second.para.value<ParaOption>().para_cur_value; break;
  case PARA_BOOL: _value = it->second.para.value<ParaBool>().para_cur_value; break;
  default: return false;
  }
  return true;
}

bool ParameterSetting::getPara(std::string _para_key, std::string _para_name, double& _value)
{
  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  std::map<std::string, ParaProperty>::iterator it = para_map_.find(key);
  if (it == para_map_.end())
  {
    return false;
  }

  ParaType para_type = it->second.para_type;
  switch (para_type)
  {
  case PARA_DOUBLE: _value = it->second.para.value<ParaDouble>().para_cur_value; break;
  default: return false;
  }
  return true;
}

bool ParameterSetting::getPara(std::string _para_key, std::string _para_name, std::string& _value)
{
  std::string key;
  key.append(_para_key).append("/").append(_para_name);
  std::map<std::string, ParaProperty>::iterator it = para_map_.find(key);
  if (it == para_map_.end())
  {
    return false;
  }

  ParaType para_type = it->second.para_type;
  switch (para_type)
  {
  case PARA_STRING: _value = it->second.para.value<ParaString>().para_cur_value; break;
  case PARA_PATH: _value = it->second.para.value<ParaPath>().para_cur_value; break;
  default: return false;
  }
  return true;
}
bool ParameterSetting::getPara(std::string _para_key, std::string _para_name, QString& _value)
{
  std::string value;
  bool rtn = getPara(std::move(_para_key), std::move(_para_name), value);
  _value   = QString::fromStdString(value);
  return rtn;
}

bool ParameterSetting::getPara(std::string _para_key, std::string _para_name, bool& _value)
{
  int value = 0;
  bool rtn  = getPara(std::move(_para_key), std::move(_para_name), value);
  _value    = static_cast<bool>(value);

  return rtn;
}

std::map<std::string, ParaProperty> ParameterSetting::getAllParaProperty()
{
  loadPara();
  return para_map_;
}

void ParameterSetting::showParaTree()
{
  loadPara();
  if (para_index_list_.size() != para_map_.size())
  {
    return;
  }
  para_tree_->clear();

  int tree_row_count = 0;
  for (auto it : para_index_list_)
  {
    ParaProperty para         = para_map_[it];
    std::string key_name      = para.key_name;
    std::string para_name     = para.para_name;
    std::string para_describe = para.para_describe;
    ParaType para_type        = para.para_type;

    std::string para_map_index;
    para_map_index.append(key_name).append("/").append(para_name);

    QList<QTreeWidgetItem*> key_list = para_tree_->findItems(key_name.data(), Qt::MatchContains);
    QTreeWidgetItem* key_item        = nullptr;
    if (key_list.size() > 0)
    {
      key_item = key_list.front();
    }
    else
    {
      key_item = new QTreeWidgetItem(para_tree_.get());
      key_item->setText(0, key_name.data());
      para_tree_->addTopLevelItem(key_item);
      tree_row_count++;
    }

    QTreeWidgetItem* para_item = new QTreeWidgetItem(key_item);
    para_item->setText(0, para_describe.data());
    switch (para_type)
    {
    case PARA_INT:
    {
      int value          = para.para.value<ParaInt>().para_cur_value;
      int min            = para.para.value<ParaInt>().para_min;
      int max            = para.para.value<ParaInt>().para_max;
      int step           = para.para.value<ParaInt>().step;
      QSpinBox* spin_box = new QSpinBox(para_tree_.get());
      spin_box->setRange(min, max);
      spin_box->setSingleStep(step);
      spin_box->setKeyboardTracking(false);
      spin_box->setValue(value);
      QObject::connect(spin_box, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged), this,
                       [=](int _value) { ParameterSetting::slotSpinBoxChange(para_map_index, _value); });
      para_tree_->setItemWidget(para_item, 1, spin_box);
    }
    break;
    case PARA_DOUBLE:
    {
      double value             = para.para.value<ParaDouble>().para_cur_value;
      double min               = para.para.value<ParaDouble>().para_min;
      double max               = para.para.value<ParaDouble>().para_max;
      int decimal              = para.para.value<ParaDouble>().decimal_num;
      double step              = para.para.value<ParaDouble>().step;
      QDoubleSpinBox* spin_box = new QDoubleSpinBox(para_tree_.get());
      spin_box->setRange(min, max);
      spin_box->setDecimals(decimal);
      spin_box->setSingleStep(step);
      spin_box->setKeyboardTracking(false);
      spin_box->setValue(value);
      QObject::connect(spin_box, static_cast<void (QDoubleSpinBox::*)(double)>(&QDoubleSpinBox::valueChanged), this,
                       [=](double _value) { ParameterSetting::slotSpinDoubleBoxChange(para_map_index, _value); });
      para_tree_->setItemWidget(para_item, 1, spin_box);
    }
    break;
    case PARA_STRING:
    {
      std::string string = para.para.value<ParaString>().para_cur_value;
      bool is_password   = para.para.value<ParaString>().is_password;
      QLineEdit* edit    = new QLineEdit(para_tree_.get());
      if (is_password)
      {
        edit->setEchoMode(QLineEdit::Password);
      }
      edit->setText(string.data());
      QObject::connect(edit, &QLineEdit::editingFinished, this,
                       [=]() { ParameterSetting::slotLineEditChange(para_map_index); });
      para_tree_->setItemWidget(para_item, 1, edit);
    }
    break;
    case PARA_OPTION:
    {
      QStringList list     = para.para.value<ParaOption>().para_list;
      int index            = para.para.value<ParaOption>().para_cur_value;
      QComboBox* combo_box = new QComboBox(para_tree_.get());
      combo_box->addItems(list);
      combo_box->setCurrentIndex(index);
      QObject::connect(combo_box, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), this,
                       [=](int _value) { ParameterSetting::slotSpinDoubleBoxChange(para_map_index, _value); });
      para_tree_->setItemWidget(para_item, 1, combo_box);
    }
    break;
    case PARA_BOOL:
    {
      bool is_check        = para.para.value<ParaBool>().para_cur_value;
      QCheckBox* check_box = new QCheckBox(para_tree_.get());
      check_box->setChecked(is_check);
      QObject::connect(check_box, &QCheckBox::clicked, this,
                       [=](bool _value) { ParameterSetting::slotCheckBoxChange(para_map_index, _value); });
      para_tree_->setItemWidget(para_item, 1, check_box);
    }
    break;
    case PARA_PATH:
    {
      std::string string      = para.para.value<ParaPath>().para_cur_value;
      bool is_folder          = para.para.value<ParaPath>().is_folder;
      PathSelectControl* ctrl = new PathSelectControl(is_folder, para_tree_.get());
      ctrl->setCurrentPath(string.data());
      QObject::connect(ctrl, &PathSelectControl::signalSelectFinish, this,
                       [=]() { ParameterSetting::slotPathControlChange(para_map_index); });
      para_tree_->setItemWidget(para_item, 1, ctrl);
    }
    break;
    }

    key_item->addChild(para_item);
    tree_row_count++;
  }
  para_tree_->expandAll();

  std::string str_title = PROJECT_NAME;
  this->setWindowTitle(str_title.data());

  int widget_height = tree_row_count * 30 + 50;
  this->resize(300, widget_height);
  this->show();
}

void ParameterSetting::loadPara()
{
  if (para_map_.size() <= 0)
  {
    return;
  }
  std::string para_path = para_path_;
  robosense::lidar::rsfsc_lib::RSFSCQSettings setting("RoboSense", para_path.data());
  setting.setIniCodec(QTextCodec::codecForName("UTF_8"));

  for (auto& it : para_map_)
  {
    ParaProperty para_property = it.second;
    std::string key;
    key.append(para_property.key_name).append("/").append(para_property.para_name);
    switch (para_property.para_type)
    {
    case PARA_INT:
    {
      int default_value   = para_property.para.value<ParaInt>().para_default;
      int cur_value       = setting.value(key.data(), default_value).toInt();
      ParaInt para        = para_property.para.value<ParaInt>();
      para.para_cur_value = cur_value;
      para_property.para.setValue(para);
    }
    break;
    case PARA_DOUBLE:
    {
      double default_value = para_property.para.value<ParaDouble>().para_default;
      double cur_value     = setting.value(key.data(), default_value).toDouble();
      ParaDouble para      = para_property.para.value<ParaDouble>();
      para.para_cur_value  = cur_value;
      para_property.para.setValue(para);
    }
    break;
    case PARA_STRING:
    {
      std::string default_value = para_property.para.value<ParaString>().para_default;
      std::string cur_value     = setting.value(key.data(), default_value.data()).toString().toStdString();
      ParaString para           = para_property.para.value<ParaString>();
      para.para_cur_value       = cur_value;
      para_property.para.setValue(para);
    }
    break;
    case PARA_OPTION:
    {
      int default_value   = para_property.para.value<ParaOption>().para_default;
      int cur_value       = setting.value(key.data(), default_value).toInt();
      ParaOption para     = para_property.para.value<ParaOption>();
      para.para_cur_value = cur_value;
      para_property.para.setValue(para);
    }
    break;
    case PARA_BOOL:
    {
      bool default_value  = para_property.para.value<ParaBool>().para_default;
      bool cur_value      = setting.value(key.data(), default_value).toBool();
      ParaBool para       = para_property.para.value<ParaBool>();
      para.para_cur_value = cur_value;
      para_property.para.setValue(para);
    }
    break;
    case PARA_PATH:
    {
      std::string default_value = para_property.para.value<ParaPath>().para_default;
      std::string cur_value     = setting.value(key.data(), default_value.data()).toString().toStdString();
      ParaPath para             = para_property.para.value<ParaPath>();
      para.para_cur_value       = cur_value;
      para_property.para.setValue(para);
    }
    break;
    }
    it.second = para_property;
  }
}

void ParameterSetting::savePara()
{
  if (para_map_.empty())
  {
    return;
  }

  // QObject::connect(spin_box, static_cast<void (QDoubleSpinBox::*)(double)>(&QDoubleSpinBox::valueChanged), this,
  //                  [=](double _value) { ParameterSetting::slotSpinDoubleBoxChange(para_map_index, _value); });

  std::string para_path = para_path_;
  robosense::lidar::rsfsc_lib::RSFSCQSettings setting("RoboSense", para_path.data());
  setting.setIniCodec(QTextCodec::codecForName("UTF_8"));

  for (auto& it : para_map_)
  {
    ParaProperty para_property = it.second;
    std::string key;
    key.append(para_property.key_name).append("/").append(para_property.para_name);
    switch (para_property.para_type)
    {
    case PARA_INT:
    {
      int para_cur_value = para_property.para.value<ParaInt>().para_cur_value;
      // setting.setValue(key.data(), para_cur_value);
      ParameterSetting::slotSpinBoxChange(key, para_cur_value);
    }
    break;
    case PARA_DOUBLE:
    {
      double para_cur_value = para_property.para.value<ParaDouble>().para_cur_value;
      // setting.setValue(key.data(), para_cur_value);
      ParameterSetting::slotSpinDoubleBoxChange(key, para_cur_value);
    }
    break;
    case PARA_STRING:
    {
      std::string para_cur_value = para_property.para.value<ParaString>().para_cur_value;
      setting.setValue(key.data(), para_cur_value.data());
      ParameterSetting::slotLineEditChange(key);
    }
    break;
    case PARA_OPTION:
    {
      int para_cur_value = para_property.para.value<ParaOption>().para_cur_value;
      setting.setValue(key.data(), para_cur_value);
      ParameterSetting::slotComboBoxChange(key, para_cur_value);
    }
    break;
    case PARA_BOOL:
    {
      bool para_cur_value = para_property.para.value<ParaBool>().para_cur_value;
      setting.setValue(key.data(), para_cur_value);
      ParameterSetting::slotCheckBoxChange(key, para_cur_value);
    }
    break;
    case PARA_PATH:
    {
      std::string para_cur_value = para_property.para.value<ParaPath>().para_cur_value;
      setting.setValue(key.data(), para_cur_value.data());
      ParameterSetting::slotPathControlChange(key);
    }
    break;
    }
  }

  std::string file_flag;
  file_flag = "file_flag/flag";
  setting.setValue(file_flag.data(), para_path.data());
}

void ParameterSetting::slotSpinBoxChange(std::string _key, int _value)
{
  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, _value);
}

void ParameterSetting::slotSpinDoubleBoxChange(std::string _key, double _value)
{
  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, _value);
}

void ParameterSetting::slotLineEditChange(std::string _key)
{
  QLineEdit* edit   = qobject_cast<QLineEdit*>(sender());
  std::string value = edit->text().toStdString();

  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, value.data());
}

void ParameterSetting::slotComboBoxChange(std::string _key, int _value)
{
  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, _value);
}

void ParameterSetting::slotCheckBoxChange(std::string _key, bool _value)
{
  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, _value);
}

void ParameterSetting::slotPathControlChange(std::string _key)
{
  PathSelectControl* ctrl = qobject_cast<PathSelectControl*>(sender());
  std::string value       = ctrl->getCurrentPath();

  std::string para_key  = splitString(_key, "/", false).front();
  std::string para_name = splitString(_key, "/", false).back();
  changePara(para_key, para_name, value.data());
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////
std::list<std::string> splitString(std::string _src_str, std::string _delim_str, bool _repeated_char_ignored)
{
  std::list<std::string> result_string_vector;
  std::replace_if(
    _src_str.begin(), _src_str.end(),
    [&](const char& _c) {
      if (_delim_str.find(_c) != std::string::npos)
      {
        return true;
      }
      else
      {
        return false;
      }
    } /*pred*/,
    _delim_str.at(0));  //将出现的所有分隔符都替换成为一个相同的字符（分隔符字符串的第一个）
  size_t pos               = _src_str.find(_delim_str.at(0));
  std::string added_string = "";
  while (pos != std::string::npos)
  {
    added_string = _src_str.substr(0, pos);
    if (!added_string.empty() || !_repeated_char_ignored)
    {
      result_string_vector.push_back(added_string);
    }
    _src_str.erase(_src_str.begin(), _src_str.begin() + pos + 1);
    pos = _src_str.find(_delim_str.at(0));
  }
  added_string = _src_str;
  if (!added_string.empty() || !_repeated_char_ignored)
  {
    result_string_vector.push_back(added_string);
  }
  return result_string_vector;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////
PathSelectControl::PathSelectControl(bool _is_folder, QWidget* _parent) : QWidget(_parent)
{
  is_folder_          = _is_folder;
  QHBoxLayout* layout = new QHBoxLayout;

  path_edit_ = new QLineEdit(this);
  path_edit_->setReadOnly(true);
  sel_button_ = new QPushButton("...", this);
  sel_button_->setMaximumWidth(30);
  layout->addWidget(path_edit_);
  layout->addWidget(sel_button_);
  layout->setContentsMargins(0, 0, 0, 0);
  this->setLayout(layout);

  connect(sel_button_, &QPushButton::clicked, this, &PathSelectControl::selectPath);
  connect(path_edit_, &QLineEdit::editingFinished, this, &PathSelectControl::slotSelectPath);
}

PathSelectControl::~PathSelectControl() {}

void PathSelectControl::selectPath()
{
  QString dir_name;
  QFileDialog file_dialog;

  if (!is_folder_)
  {
    dir_name = file_dialog.getOpenFileName(this, "选择文件", "./", "ALL(*.*)");
  }
  else
  {
    dir_name = file_dialog.getExistingDirectory(this, "选择文件夹", "./");
  }
  current_path_ = dir_name.toStdString();
  path_edit_->setText(dir_name);

  signalSelectFinish();
}

void PathSelectControl::setCurrentPath(std::string _path)
{
  current_path_ = _path;
  path_edit_->setText(_path.data());
}

void PathSelectControl::slotSelectPath()
{
  QString dir = path_edit_->text();
  setCurrentPath(dir.toStdString());

  signalSelectFinish();
}
