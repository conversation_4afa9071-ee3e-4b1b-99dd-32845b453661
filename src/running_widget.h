﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RUNNING_WIDGET_H
#define RUNNING_WIDGET_H

#include "widget_lidar_info.h"
#include <QtWidgets>
#include <qcheckbox.h>
#include <qcombobox.h>
#include <rsfsc_msg.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class AgingProgressWidget;

class RunningWidget : public QWidget
{
  Q_OBJECT
public:
  friend class AgingProgressWidget;
  RunningWidget(int _lidar_index, QWidget* _parent = nullptr);
  virtual ~RunningWidget();
  int getLogIndex();

public:
  void showWidget(bool _is_running);
  QString getLidarSn();
  QString getLidarProject();
  robosense::lidar::rsfsc_lib::ProjectCode getLidarProjectCodeIndex();
  robosense::lidar::rsfsc_lib::WidgetLidarInfo* getLidarInfoWidgetPtr() { return widget_lidar_info_; }

  void setOperateEnabled(bool _enabled);

private Q_SLOTS:
  void slotStartAgingRunning();
  void slotTestRelay(bool _is_turn_on);

Q_SIGNALS:
  void signalEnterPressed();
  void signalRunning(bool _is_start);
  void signalSkipOpticalTest(bool _is_skip);

private:
  rsfsc_lib::WidgetLidarInfo* widget_lidar_info_ = nullptr;
  QPushButton* one_key_run_button_               = nullptr;
  QComboBox* project_code_combo_box_             = nullptr;
  QCheckBox* test_relay_button_                  = nullptr;
  QCheckBox* checkbox_fsm_stress_test_           = nullptr;
  QCheckBox* checkbox_fsm_change_ip_             = nullptr;
  QCheckBox* checkbox_fsm_encoding_calib_        = nullptr;
  QCheckBox* checkbox_fsm_chn_angle_write_       = nullptr;
  QCheckBox* checkbox_fsm_vbd_calib_             = nullptr;
  QCheckBox* checkbox_fsm_firmware_update_       = nullptr;
  QCheckBox* checkbox_fsm_aging_                 = nullptr;
  QCheckBox* checkbox_fsm_clear_calib_data_      = nullptr;

  bool is_operate_enabled_ = false;
  robosense::lidar::rsfsc_lib::ProjectCode selected_project_ =
    robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_NOT_FIND;
  int lidar_index_ = 0;
};
}  // namespace lidar
}  // namespace robosense

#endif  // RUNNING_WIDGET_H
