﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "relay_controller.h"
#include "app_event.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QDebug>

namespace robosense
{
namespace lidar
{
RelayController::RelayController(QObject* _parent) : QObject(_parent)
{
  connect(this, &RelayController::signalRelayTurn, this, &RelayController::slotRelayTurn, Qt::QueuedConnection);
};

RelayController::~RelayController() = default;

RelayController* RelayController::getInstance()
{
  static RelayController hw;
  return &hw;
}

bool RelayController::initRelayController(const RelayLayout& _relay_layout, const std::vector<std::string>& _port_name)
{
  bool rtn      = true;
  relay_layout_ = _relay_layout;
  for (size_t i = 0; i < _port_name.size(); i++)
  {
    if (_port_name[i] == "")
    {
      robosense::lidar::RSFSCLog::getInstance()->error("系统串口数量不足,无法满足生产需求,请检查");
      continue;
    }

    relay_controller_driver_[i].reset((RelayControllerFactory::createRelayController(
      RelayControllerFactory::ZHONG_KAI_CONTROLLER_16, { _port_name[i] })));
    std::string err_msg;
    if (!relay_controller_driver_[i]->connectController(err_msg))
    {
      RSFSCLog::getInstance()->error(
        QString("雷达继电器%1,[%2]连接失败:").arg(i + 1).arg(QString::fromStdString(_port_name[i])).toStdString() +
        err_msg);
      rtn = false;
    }
  }
  return rtn;
}

bool RelayController::relayTurnPrivate(int _lidar_index, bool _is_turn_on)
{
  bool rtn = false;

  int relay_module_index   = 0;
  int relay_module_channel = 0;

  switch (relay_layout_)
  {
  case RELAY_LAYOUT0_HHL:
  {
    rtn = getRelayIndexMems(_lidar_index, relay_module_index, relay_module_channel);
    break;
  }
  case RELAY_LAYOUT0_SS:
  case RELAY_LAYOUT0_NORMAL:
  {
    rtn = getRelayIndexHelios(_lidar_index, relay_module_index, relay_module_channel);
    break;
  }
  default: rtn = getRelayIndexHelios(_lidar_index, relay_module_index, relay_module_channel); break;
  }

  if (!rtn)
  {
    robosense::lidar::RSFSCLog::getInstance(_lidar_index + 1)->error("雷达序号错误,无法操作继电器");
    return rtn;
  }
  std::string err_msg;

  if (relay_controller_driver_[relay_module_index])
  {
    if (_lidar_index == 0xff)
    {
      _is_turn_on ? rtn = relay_controller_driver_[relay_module_index]->turnOnAllChannel(err_msg)
                  : rtn = relay_controller_driver_[relay_module_index]->turnOffAllChannel(err_msg);
    }
    else
    {
      _is_turn_on ? rtn = relay_controller_driver_[relay_module_index]->turnOnChannel(relay_module_channel, err_msg)
                  : rtn = relay_controller_driver_[relay_module_index]->turnOffChannel(relay_module_channel, err_msg);
    }

    if (err_msg.length() > 0)
    {
      robosense::lidar::RSFSCLog::getInstance(_lidar_index + 1)->error("继电器操作失败:" + err_msg);
    }
  }
  RSFSCLog::getInstance(_lidar_index + 1)
    ->debug("lidar_index: {}, relay_index: {}", _lidar_index + 1, relay_module_index);

  return rtn;
}

bool RelayController::getRelayIndexHelios(int _lidar_index, int& _relay_module_index, int& _relay_module_channel)
{
  int max_relay_channel = G_MAX_RELAY_NUM * 16;
  if (_lidar_index < 0 || _lidar_index >= max_relay_channel)
  {
    return false;
  }
  _relay_module_index   = _lidar_index / 16;
  _relay_module_channel = _lidar_index % 16 + 1;

  return true;
}

bool RelayController::getRelayIndexMems(int _lidar_index, int& _relay_module_index, int& _relay_module_channel)
{
  int max_relay_channel = G_MAX_RELAY_NUM * 16;
  if (_lidar_index < 0 || _lidar_index >= max_relay_channel)
  {
    return false;
  }
  int col   = 8;
  int group = col / 4;
  if (col % 4 > 0)
  {
    group++;
  }

  _relay_module_index = _lidar_index / 4;
  _relay_module_index %= group;
  _relay_module_channel = (_lidar_index / 4 / group * 4) + (_lidar_index % 4) + 1;

  return true;
}

bool RelayController::relayTurn(int _lidar_index, bool _is_turn_on)
{
  OperationContext context;

  signalRelayTurn(_lidar_index, _is_turn_on, &context);

  context.wait();

  return context.success;
}

void RelayController::slotRelayTurn(int _lidar_index, bool _is_turn_on, OperationContext* _context)
{
  ContextGuard guard(_context);
  _context->success = relayTurnPrivate(_lidar_index, _is_turn_on);
  if (_context->success)
  {
    app()->signalUpdateRelayState(_lidar_index, _is_turn_on);
  }
}

}  // namespace lidar
}  // namespace robosense
