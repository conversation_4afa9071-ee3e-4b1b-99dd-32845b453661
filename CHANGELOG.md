﻿## v3.0.1.2025.0610
### Feat
- 完整支持0351

## v3.0.0.2025.0605
### Feat
- 调整整体的fsm框架与通信库框架

## v2.15.10.2025.0529
### Feat
- 增加项目编号0351、0352、0360
- 增加所有寄存器的回读校验
- 增加码盘标定的等待码盘状态的回读校验
- 增加人工确认回读校验的功能

## v2.15.9.2025.0529
### Fix
- 优化udp停止可能引发的闪退问题，在断开连接时候不进行跨线程停止

## v2.15.8.2025.0520
### Fix
- 优化udp停止可能引发的闪退问题
- 优化tcp的parser中clear queue可能引发的闪退问题

## v2.15.7.2025.0519
### Feat
- 修正获取md5为空的问题

## v2.15.6.2025.0519
### Feat
- 修正获取mes固件信息失败的问题

## v2.15.5.2025.0508
### Feat
- 修正没有勾选修改IP时候，不应恢复IP
- 修正不勾选固件升级时，不去拉mes的固件信息
- 修正mes获取数据后不返回中断，而是返回失败

## v2.15.1.2025.0508
### Feat
- 增加点击开始后后立刻`disable`控件
- 增加从`MES`中获取当前的固件版本`md5`信息
- 增加自动在固件目录查询检索到对应固件
- 增加`MES`上的版本号卡控

## v2.14.10.2025.0429
### Feat
- 修正mes问题

## v2.14.9.2025.0429
### Feat
- 增加老化面积模式

## v2.14.8.2025.0425
### Feat
- 增加经验零度角的写入

## v2.14.7.2025.0424
### Feat
- 添加清除标定数据的功能
- 添加固件升级前的版本
- 去除后处理的寄存器配置
### Fix
- 优化闪退问题

## v2.14.6.2025.0424
### Feat
- 添加校验垂直角功能，如果超差则使用仿真值

## v2.14.5.2025.0423
### Feat
- 增加固件升级等待点云包后再进行
- 增加固件升级前进行转速检查

## v2.14.3.2025.0422
### Feat
- 修正通道角写入误报的问题

## v2.14.2.2025.0421
### Feat
- 增加通道角写入

## v2.14.1.2025.0418
### Feat
- 增加VBD的标定

## v2.13.6.2025.0419
### Feat
- 增加寄存器的配置，关闭动静绝反，旁路反标

## v2.13.4.2025.0407
### Feat
- 修正抓包引起的端口冲突问题

## v2.13.3.2025.0403
### Feat
- 指定抓包的网口

## v2.13.2.2025.0331
### Feat
- 增加压测抓包的功能

## v2.13.1.2025.0328
### Feat
- 增加SN的校验

## v2.12.5.2025.0328
### Feat
- 增加SN的校验

## v2.12.4.2025.0319
### Feat
- 修正IP池冲突时候difop异常

## v2.12.3.2025.0318
### Fix
- 修正program unique code

## v2.12.2.2025.0318
### Fix
- 修正修改完IP后再进行确认绑定

## v2.12.1.2025.0318
### Fix
- 添加mes获取mac地址

## v2.11.2.2025.0314
### Fix
- 修正IP返回失败

## v2.11.1.2025.0307
### Feat
- 增加IP池功能
- 添加SN不匹配时候的提报不良

## v2.10.7.2025.0224
### Fix
- 修正光通误码测试，数据未上传的问题

## v2.10.6.2025.0224
### Fix
- 修正关闭软件时候的崩溃问题
- 修正操作继电器可以能引发的崩溃问题
- 修正"打开数据文件夹"为"打开结果文件夹"
- 修复误触关闭按钮时，触发的资源释放问题

## v2.10.5.2025.0220
### Fix
- 修正老化时间不正确的问题
- 修正不进行修改IP时，直接跳过固件升级的问题

## v2.10.4.2025.0218
### Fix
- 修正失败后未正确上传的问题
- 修正初始化绑定difop端口冲突的问题

## v2.10.3.2025.0210
### Fix
- 修正跳过时候，状态机不正确的问题
- 修正老化各项的预计耗时
- 删除压测转速参数配置
- 修正`MSOP`监控长度与码盘标定采集次数放入`config`文件中
- 修正码盘标定失败时候的连接`IP`不正确的问题

## v2.10.2.2025.0208
### Feat
- 修正老化时间
- 修正失败保留现场不做任何操作
- 增加技术员操作员的权限管控
### Fixed
- 转速阈值收敛
- 主板总输入电压阈值调整

## v2.10.1.2025.0122
### Feat
- 修复修改完成IP后，第二次连接失败时候恢复雷达IP失败的问题
- 增加码盘标定失败次数尝试

## v2.10.0.2025.0120
### Feat
- 增加固件升级后的配置文件校验
- 增加恢复ip失败的修改次数
- 增加了码盘标定回读的校验
- 增加了光通误码数的获取次数

### Fix
- 修正中断时候，不生成成result，防止上传MES

### Fix
- 修正`top_input_vol`与`top_3v8`阈值范围

## v2.9.0.2025.0112
### Feat
- 增加对`log index`的支持
### Fix
- 修正`top_input_vol`主板总输入电压
- 修正增加失败后恢复雷达IP的等待时间

## v2.8.0.fix.2025.0103
### Feat
- 修正光通误码测试顺序
- 修正大概文件读取配置时候崩溃的问题

## v2.8.0.2025.0103
### Feat
- 新增上下行光通误码测试

## v2.7.0.2024.1223
### Fixed
- 新增日志库，指定单个index的日志路径

## v2.6.5.20241219
### Fixed
- 修正不卡控主板阈值
- 修正主板阈值字段名

## v2.6.4.20241216
### Fixed
- 修正12.13日更新的阈值

## v2.6.3.20241210
### Fixed
- 修正未启动老化时候，`work model`为空时则不打开数据文件夹

## v2.6.2.20241206
### Fixed
- 修正修改IP时候不等待顶板启动

## v2.6.1.20241206
### Fixed
- 修正三色灯继电器index越界的问题

## v2.6.0.20241205
### Added
- 增加三色灯支持

### Fixed
- 修正中断无法恢复IP的问题
- 修正压测前未启用监控difop的问题
- 修正失败未更新运行状态的问题
- 修正允许压测失败

## v2.5.4.20241203
### Fixed
- 修正码盘标定

## v2.5.0.20241202
### Fixed
- 修正增加写入config配置的延迟时间

## v2.4.1.20241126
### Fixed
- 修正寄存器固化问题

## v2.4.0.20241125
### Feat
- 增加固件升级功能

## v2.2.2.20241123
### Fixed
- 修正超限中断后，不写入result结果的问题

## v2.2.1.20241121
### Fixed
- 修正码盘标定校验异常问题

## v2.2.0.20241119
### Added
- 修复崩溃问题

## v2.1.2.20241115
### Fixed
- 修复崩溃问题

## v2.1.1.20241115
### Added
- 修正lib git2找不到的问题

## v2.1.0.20241115
### Added
- 增加mac地址修改

## v2.0.3.20241114
### Fixed
- 修复多项bug

## v2.0.2.20241113
### Fixed
- 修复开始老化时候的崩溃问题

## v2.0.1.20241111
### Feat
- 更新框架
- 添加继电器layout

## v1.3.4.20240511
### Fix
- 调整`0335、0303、0320`的`HV`监控范围为

## v1.3.3.20240314
### Fix
- 添加功能，可跳过光通测试

## v1.3.2.20240118
### Fix
- 添加机制减少超时问题

## v1.3.1.20240112
### Feat
- 增加了配置压测转速的选项

## v1.3.0.20240108
### Feat
- 增加0320、0303、0335光通误码率测试

## v1.2.2.20240105
### Fix
- 更新通信库，增加稳定性
- 添加三色灯的使用
- 增加设置项，是否运行失败

## v1.2.1.20240104
### Fix
- 修复一拖多崩溃的问题

## v1.2.0.20240103
### Feat
- 添加继电器状态显示
- 增加读取雷达配置的时间到5秒
- 更新通信库版本

## v1.1.3.20231211
### Fixed
- 更新通信库，优化卡死问题

## v1.1.2.20231208
### Fixed
- 调整温度`limit`最低值为0
- 在`MechTcp`析构的时候，解决软件崩溃卡死的问题

## v1.1.1.20231129
### Fixed
- 调整`3V8`的阈值扩大`0.1V`

## v1.0.3.20231127
### Fixed
- 增加回读寄存器校验
- 降低数据采样速度
- 切换通信库至`boost`分支
- 连接成功后增加`power on`操作

## v1.0.2.20231116
### Fixed
- 压测不修改转速版本

## v1.0.1.20231115
### Fixed
- 状态重置取消继电器断电操作
- 压测失败时，保留失败现场
- 增加老化和压测过程中的失败弹窗
- 修正状态机退出时成员清理顺序

## v1.0.0.20231031
### Fixed
- 修正了读取寄存器显示的名字错误
- 增加退出状态机操作，在软件关闭的时刻
- 读取老化数据失败时候，取消重启，保留现场排查问题

## v0.7.0.20231024
### Feat
- 根据雷达类型，限制项目编号的选择
- 增加限制，同一时间只有一台雷达处于修改`IP`的状态
- `Helios`系列在写入`IP`时强制设置工作模式
- 修复固件特定版本下导致触发边缘设置错误的问题

## v0.6.2.20231024
### Fixed
- 调整`BPearl`的`top_vbus`电压到范围`11V~15.5V`

## v0.6.1.20231023
### Fixed
- 修正压测读取转速失败的问题
- 修复老化最后一次读取数据异常的问题

## v0.6.0.20231020
### Fixed
- 增加`top_2V5`的异常屏蔽值`3.122`和`1.875`
- 修改`0335`的范围至`-3.6~-3`
- 恢复雷达`IP`的时候不进行转速恢复
- 增加压测前的转速检查，不满足条件时候再次进行转速设置

## v0.5.0.20231018
### Feat
- 修改雷达转速获取方式，转速来源自雷达配置信息

## v0.4.8.20231016
### Feat
- 增加`SN`防重复的检测
- 增加输出文件的`PS`和`PL`版本的输出
### Fixed
- 修复二次停止老化出现的未正确恢复的问题
- 修复项目编号不匹配的时候仍然可以进行老化的问题

## v0.4.7.20231011
### Fix
- 修复跳过老化时，出现闪退的`bug`

## v0.4.6.20231011
### Fix
- 对`0303`的`top_A5V`进行特殊处理，屏蔽`0.62、4.37、5.6`三个异常值判定通过

## v0.4.5.20231010
### Fix
- 对`0320`的`top_5V`进行特殊处理，屏蔽`0.62、4.37、5.6`三个异常值判定通过

## v0.4.3.20231008
### Fix
- 修复老化失败或中断时候状态切换错误的`bug`

## v0.4.2.20231007
### Fix
- 修改初始化后的等待时间

## v0.4.1.20231007
### Fix
- 调整`0303`的`top_N5v`的范围`-4.6V~-5.2V`

## v0.4.0.20230927
### Fix
- 在`ping`成功后增加延迟时间
- 调整冷却`PASS`的条件至冷却结束
- 修改`top_vbus`标准`11V-15V`
### Feat
- 增加三色灯支持
- 适配3行10列的布局

## v0.3.0.20230926
### Fix
- 增加`Debug`模式
- 增加恢复雷达`IP`时候的等待延迟时间
- 添加`logger`生成`log`文件
- `0335`的`top_vbus`标准

## v0.2.7.20230925
### Fix
- 修改`BPearl 0335`的`top_vbus`标准

## v0.2.6.20230922
### Fix
- 修改`Ruby 0303`的`top_N5V`的标准

## v0.2.5.20230921
### Fix
- 修改`BPearl 0335`的`N3V3`的标准
- 优化老化数据输出的文件
- 修复状态重置功能

## v0.2.4.20230915
### Fix
- 解决压测过程中`udp`未完全关闭的问题
- 增加跳过老化功能

## v0.2.3.20230913
### Feat
- 将`data`数据输出为一个文件
### Fix
- 修复最后一次读取完数据后立马进入压测的`bug`

## v0.2.2.20230912
### Feat
- 输出的寄存器数据文件增加SN记录

## v0.2.1.20230908
### Fix
- 修复抓`udp`包时候`stop`偶尔出现的无法停止的情况

## v0.2.0.20230907
### Feat
- 优化软件在测试过程中关闭出现的崩溃问题
- 添加ping功能

## v0.1.6.20230906
### Fix
- 优化`停止老化`的过程
- 重写`sleep`逻辑
- 临时增加压测继电器日志

## v0.1.5.20230905
### Fix
- 修复日志索引显示错误

## v0.1.4.20230905
### Fix
- 修改0335、0320项目在老化结束，进入压力测试前修改恢复转速为600
- 修改0335的监控标准
- 修正PS与PL的版本异常问题
- 增加日志打印

## v0.1.3.20230901
### Feat
- 第一次连接失败时候尝试连接修正IP
- 添加压测结果到`mes`结果

## v0.1.2.20230831
### Fixed
- 修复MSOP包冲突的问题

## v0.1.1.20230830
### Feat
- 增加压力测试检查点云包
- 增加压力测试参数配置

## v0.1.0.20230830
### Feat
- 增加0303 ruby与0335 bpearl项目的支持
- 修复继电器分组错误
- 删除冗余的公式计算，以csv公式来演算真实值
- 在读取寄存器数据前加入等待链路状态的动作
- 修复多种情况下，lidar无法正确恢复IP的bug
- 增加0303、0335的类型检查

## v0.0.5.20230823

### Feat
- 修复继电器分组问题

## v0.0.4.20230822

### Feat
- 优化软件，较上版本更为稳定

## v0.0.3.20230819

### Feat
- 更新通信库版本

## v0.0.2.20230816

### Feat
- 支持老化过后进行压力测试

## v0.0.1.20230812

### Feat
- 支持`0320`寄存器监控，基础功能正常

## v0.0.1.20230801

### Feat

- 初始化版本
- 从`mems_aging`项目中进行了修改，应用于机械式雷达的老化
