﻿# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
apd_temp,0,110,<PERSON><PERSON><PERSON>,APD温度(Apd_Temp)
top_under_temp,0,120,<PERSON><PERSON><PERSON>,主板 底面温传(Top_UnderTemp)
top_above_temp,0,120,<PERSON><PERSON><PERSON>,主板 顶面温传(Top_AboveTemp)
top_fpga_temp,0,120,<PERSON><PERSON><PERSON>,主板 FPGA温度(Top_FPGA Temp)
bot_fpga_temp,0,120,<PERSON><PERSON><PERSON>,底板 FPGA温度(Chip OnTemp)
top_2V5,2.4,2.6,V,主板 2.5v(Top_2V5)
top_vbus,23,34,V,主板 VBUS(Top_Vbus)
top_tx5V,4.8,5.2,V,主板 Tx5v(Top_Tx5V)
top_A5V,4.8,5.2,V,主板 A5v(Top_A5)
top_HV,100,200,V,主板 HV(Top_HV)
top_N5V,-5.4,-4.6,V,主板 N5v(Top_N5V)
machine_vbus,9,36,V,整机 VBUS(Machine_Vbus)
bot_5V,4.8,5.5,V,底板 5v(Bot_5V)
bot_28V,22,30,V,底板 28V(Bot_28V)
cbus_curr,0.1,1.7,A,整机电流CBUS (Machine Current)
real_speed,595,605,RPM,实时转速
stress_test,0,30,unitless,压力测试通过次数
upload_error_rate,0,0,unitless,上行误码率
download_error_rate,0,0,unitless,下行误码率