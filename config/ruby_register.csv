﻿# 寄存器名称：在程序中生成 '寄存器名称 - 数值结构体'的 std::map　直接通过名称来索引对应的数值结构体 从而在程序中进行寄存器的相关读写操作,,,,,,,,
# 寄存器地址：如果是多个连续地址的 写首地址 加0x前缀 寄存器地址小写,,,,,,,,
# 寄存器个数（最小值为1）：以当前寄存器地址为起始地址 连续读取的寄存器数量 (个数为包含首地址的10进制数) 连续寄存器地址默认间隔为4,,,,,,,,
# 寄存器模式(0 1 2): 在程序中 首先读取寄存器模式数值 再决定去读取该模式的相关数值,,,,,,,,
#                ,"寄存器模式0 表示不改变寄存器数值的""只读操作""",,,,,,,
#                ,"寄存器模式1 表示改变后恢复寄存器原始数值的""读写操作""(本模式需要读取 '标定时设定值' 和 '标定后恢复值')",,,,,,,
#                ,"寄存器模式2 表示改变后保留寄存器新数值的""读写操作""（本模式读取 '寄存器最大值'和'寄存器最小值'）",,,,,,,
# 标定时设定值：  标定时需要设定的寄存器值(加0x前缀的16进制数),,,,,,,,
# 标定后恢复值：  标定完成需要恢复的寄存器值(加0x前缀的16进制数),,,,,,,,
# 寄存器最小值：  寄存器允许写入的最小值(加0x前缀的16进制数),,,,,,,,
# 寄存器最大值：  寄存器允许写入的最大值(加0x前缀的16进制数),,,,,,,,
# 备注：          寄存器含义以及其参数含义,,,,,,,,
# 特殊说明１：csv读取是以　'逗号'　来划分格子，注意　'备注'　中，不要添加逗号，否则程序无法完全读取备注信息,,,,,,,,
# 特殊说明２：'寄存器名称'和备注均设置为与 'reg_map_mems_20210622.xlsm'统一的内容　并注意适当变通,,,,,,,,
,,,,,,,,
寄存器名称, 寄存器地址, 寄存器个数,寄存器模式, 标定时设定值, 标定后恢复值, 寄存器最小值, 寄存器最大值, 备注
apd_temp,0x83c18024,1,monitor,,,0,0xf,200*(data/4096)-50,APD温度(Apd_Temp)
top_under_temp,0x83c18014,1,monitor,,,0,0xf,data,主板 底面温传(Top_UnderTemp)
top_above_temp,0x83c18018,1,monitor,,,0x10,0x1f,data,主板 顶面温传(Top_AboveTemp)
top_fpga_temp,0x83c18028,1,monitor,,,0x10,0x1f,503.975*data/4096-273.15,主板 FPGA温度(Top_FPGA Temp)
bot_fpga_temp,0x83c00110,1,monitor,,,0,0x1f,503.975*data/4096-273.15,底板 FPGA温度(Chip OnTemp)
top_2V5,0x83c18008,1,monitor,,,0,0xf,10*data/4096,主板 2.5v(Top_2V5)
top_vbus,0x83c18004,1,monitor,,,0,0xf,42*data/4096,主板 VBUS(Top_Vbus)
top_tx5V,0x83c1800c,1,monitor,,,0,0xf,10*data/4096,主板 Tx5v(Top_Tx5V)
top_A5V,0x83c1800c,1,monitor,,,0x10,0x1f,10*data/4096,主板 A5v(Top_A5)
top_HV,0x83c18010,1,monitor,,,0,0xf,(1.661-data/4096)/0.00658,主板 HV(Top_HV)
top_N5V,0x83c18010,1,monitor,,,0x10,0x1f,-(0.9087-data/4096)/0.0725,主板 N5v(Top_N5V)
machine_vbus,0x83c00134,1,monitor,,,0,0x1f,data/4096/0.020833,整机 VBUS(Machine_Vbus)
bot_5V,0x83c00130,1,monitor,,,0,0x1f,data/4096/0.1,底板 5V(Bot_5V)
bot_28V,0x83c0012C,1,monitor,,,0,0x1f,data/4096/0.020833,底板 28V(Bot_28V)
cbus_curr,0x83c00138,1,monitor,,,0,0x1f,5.5*data/4096,整机电流CBUS (Machine Current)
real_speed,0x2001,1,monitor_skip,,,0,0x1f,data,实时转速
init_motor_speed,0x1007,1,init_cmd,0x2,,,,设置转速600(注：此非寄存器读写)
