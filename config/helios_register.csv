﻿# 寄存器名称：在程序中生成 '寄存器名称 - 数值结构体'的 std::map　直接通过名称来索引对应的数值结构体 从而在程序中进行寄存器的相关读写操作,,,,,,,,
# 寄存器地址：如果是多个连续地址的 写首地址 加0x前缀 寄存器地址小写,,,,,,,,
# 寄存器个数（最小值为1）：以当前寄存器地址为起始地址 连续读取的寄存器数量 (个数为包含首地址的10进制数) 连续寄存器地址默认间隔为4,,,,,,,,
# 寄存器模式(0 1 2): 在程序中 首先读取寄存器模式数值 再决定去读取该模式的相关数值,,,,,,,,
#                ,"寄存器模式0 表示不改变寄存器数值的""只读操作""",,,,,,,
#                ,"寄存器模式1 表示改变后恢复寄存器原始数值的""读写操作""(本模式需要读取 '标定时设定值' 和 '标定后恢复值')",,,,,,,
#                ,"寄存器模式2 表示改变后保留寄存器新数值的""读写操作""（本模式读取 '寄存器最大值'和'寄存器最小值'）",,,,,,,
# 标定时设定值：  标定时需要设定的寄存器值(加0x前缀的16进制数),,,,,,,,
# 标定后恢复值：  标定完成需要恢复的寄存器值(加0x前缀的16进制数),,,,,,,,
# 寄存器最小值：  寄存器允许写入的最小值(加0x前缀的16进制数),,,,,,,,
# 寄存器最大值：  寄存器允许写入的最大值(加0x前缀的16进制数),,,,,,,,
# 备注：          寄存器含义以及其参数含义,,,,,,,,
# 特殊说明１：csv读取是以　'逗号'　来划分格子，注意　'备注'　中，不要添加逗号，否则程序无法完全读取备注信息,,,,,,,,
# 特殊说明２：'寄存器名称'和备注均设置为与 'reg_map_mems_20210622.xlsm'统一的内容　并注意适当变通,,,,,,,,
,,,,,,,,
寄存器名称, 寄存器地址, 寄存器个数,寄存器模式, 标定时设定值, 标定后恢复值, 寄存器最小值, 寄存器最大值, 备注
apd_avg_temp,0x301f,1,top,,,0,0xf,data/256,接收板APD平均温度
top_under_temp,0x83c18024,1,monitor,,,0,0xf,200*(data/4096)-50,顶板底面温度
top_fpga_temp,0x83c18028,1,monitor,,,0x10,0x1f,503.975*data/4096-273.15,顶板fpga温度
bot_fpga_temp,0x83c00128,1,monitor,,,0,0xf,503.975*data/4096-273.15,底板fpga温度
top_5V,0x83c1800c,1,monitor,,,0x10,0x1f,10*data/4096,顶板5V
top_NHV,0x83c18010,1,monitor,,,0,0xf,-(1.71376-(data/4096))/0.009425,顶板-HV
top_fpga_vol,0x83c1802c,1,monitor,,,0x10,0x1f,3*data/4096,顶板fpga电压(1V0)
top_fpga_aux,0x83c1802c,1,monitor,,,0,0xf,3*data/4096,顶板fpga-aux(1V8)
bot_12V,0x83c00114,1,monitor,,,0,0xb,24.5*data/4096,底板 12V
bot_5V,0x83c00118,1,monitor,,,0,0xb,11*data/4096,底板 5V
bot_fpga_vccint,0x83c00120,1,monitor,,,0,0xb,3*data/4096,底板 FPGA vccint（1v）
bot_fpga_vccaux,0x83c00124,1,monitor,,,0,0xb,3*data/4096,底板 FPGA Vccaux（1.8v）
cbus_curr,0x83c00108,1,monitor,,,0,0x1f,5*data/4096,12V电流CBUS
real_speed,0x83c01054,1,monitor_skip,,,0,0x1f,data,实时转速
init_motor_speed,0x83c01000,1,init,0x4b0,0x258,,,设置转速
