﻿# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
fsm_ping,1,1,unitless,ping状态,fsm_
fsm_connect_before,1,1,unitless,首次连接状态,fsm_
fsm_connect_after,1,1,unitless,修改ip后连接状态,fsm_
fsm_change_ip,1,1,unitless,修改ip状态,fsm_
fsm_firmware_update_unzip,1,1,unitless,解压固件状态,fsm_
fsm_firmware_update_app,1,1,unitless,升级app固件状态,fsm_
fsm_firmware_update_bot,1,1,unitless,升级底板固件状态,fsm_
fsm_firmware_update_motor_speed,580,620,unitless,升级前的转速检查,fsm_
fsm_firmware_update_top,1,1,unitless,升级顶板固件状态,fsm_
fsm_firmware_update_write_config,1,1,unitless,写入配置文件状态,fsm_
fsm_firmware_update_reboot,1,1,unitless,重启状态,fsm_
fsm_firmware_update_check,1,1,unitless,固件升级校验状态,fsm_
fsm_difop_timeout,0,60,unitless,difop超时状态,fsm_
fsm_connect_lidar,1,1,unitless,连接雷达状态,fsm_
fsm_stress_success_count,nan,nan,unitless,压力测试成功次数,fsm_
fsm_init_lidar,1,1,unitless,初始化雷达状态,fsm_
fsm_init_read_data,1,1,unitless,初始化读取数据状态,fsm_
fsm_init_stress_test,1,1,unitless,初始化压力测试状态,fsm_
fsm_lidar_sn_match,1,1,unitless,读取的雷达SN与扫码SN匹配,fsm_
fsm_bound_ip_pool,1,1,unitless,绑定ip池状态,fsm_
fsm_msop_channel,1,1,unitless,点云状态,fsm_
fsm_restore_ip,1,1,unitless,恢复ip状态,fsm_
fsm_encod_calib_motor_stable,1,1,unitless,码盘标定稳定状态,fsm_
fsm_encod_calib_collect,1,1,unitless,码盘标定数据采集,fsm_
fsm_encod_calib_process,1,1,unitless,码盘标定数据处理,fsm_
fsm_encod_calib_check,1,1,unitless,码盘标定数据校验,fsm_
fsm_encod_calib_write,1,1,unitless,码盘标定数据固化写入,fsm_
fsm_encod_calib_save,1,1,unitless,码盘标定数据生效失败,fsm_
fsm_encod_calib_read,1,1,unitless,码盘标定数据回读,fsm_
fsm_encod_calib_read_check,1,1,unitless,码盘标定数据回读校验,fsm_
fsm_up_light_test_en,1,1,unitless,光通上行测试控制,fsm_
fsm_up_light_test_total,1,nan,unitless,光通上行测试总数据量,fsm_
fsm_up_light_test_error,0,0,unitless,光通上行测试误码数,fsm_
fsm_up_light_test_error_rate,0,0,unitless,光通上行误码率,fsm_
fsm_down_light_test_en,1,1,unitless,光通下行测试控制,fsm_
fsm_down_light_test_total,1,nan,unitless,光通下行测试总数,fsm_
fsm_down_light_test_error,0,0,unitless,光通下行测试误码数,fsm_
fsm_down_light_test_error_rate,0,0,unitless,光通下行误码率,fsm_
fsm_light_test_state,1,1,unitless,光通测试状态,fsm_
fsm_up_error_rate,0,0,unitless,上行误码率,fsm_
fsm_down_error_rate,0,0,unitless,下行误码率,fsm_
fsm_state,pass,pass,text,fsm状态,fsm_
reboot_lidar,1,1,unitless,重启雷达,fsm_
require_ver_angle,1,1,unitless,MES获取垂直水平角度,fsm_
ver_angle_error,-0.35,0.35,unitless,垂直角度误差,fsm_
write_chn_angle,1,1,unitless,写入垂直水平角度,fsm_
check_chn_angle,1,1,unitless,校验垂直水平角度,fsm_
require_customer_info,1,1,unitless,MES获取mac地址、版本信息,fsm_
mes_mac_address,1,1,text,MES中的Mac地址,fsm_
require_vbd_vol,1,1,unitless,从MES获取VBD电压,fsm_
vbd_voltage,-21.3,-20.4,V,VBD电压,fsm_
vbd_read_data,1,1,unitless,读取VBD数据,fsm_
vbd_write_data,1,1,unitless,写入VBD数据,fsm_
vbd_check_data,1,1,unitless,回读校验VBD数据,fsm_
vbd_calib_status,1,1,unitless,校验VBD标定CRC状态,fsm_
clear_calib_data,1,1,unitless,清除标定数据,fsm_
write_exp_zero_angle,1,1,unitless,经验零度角,fsm_
top_version_before,nan,nan,unitless,升级前顶板固件版本,fsm_
bot_version_before,nan,nan,unitless,升级前底板固件版本,fsm_
app_version_before,nan,nan,unitless,升级前APP固件版本,fsm_
motor_version_before,nan,nan,unitless,升级前电机固件版本,fsm_
check_top_firmware_version,nan,nan,unitless,顶板固件版本,fsm_
check_bot_firmware_version,nan,nan,unitless,底板固件版本,fsm_
check_app_firmware_version,nan,nan,unitless,APP固件版本,fsm_
check_motor_firmware_version,nan,nan,unitless,电机固件版本,fsm_
check_config_firmware_version,nan,nan,unitless,配置文件版本,fsm_
human_confirm,1,1,unitless,人工确认回读是否一致,fsm_
motor_set_speed,600,600,RPM,电机设定转速,difop
top_firmware_version,nan,nan,unitless,顶板固件版本,difop
bot_firmware_version,nan,nan,unitless,底板固件版本,difop
app_firmware_version,nan,nan,unitless,APP固件版本,difop
motor_firmware_version,nan,nan,unitless,电机固件版本,difop
realtime_speed,598,602,RPM,电机实时转速,difop
bot_vbus,7.2,38.4,V,底板输入电压,difop
bot_ibus,0,1.67,A,底板输入电流,difop
bot_sys_5v,4.25,5.75,V,底板5V电压,difop
bot_sys_12v,10.2,13.8,V,底板12V电压,difop
bot_vcco_psio0,3.036,3.564,V,底板片内监控电压3.3V,difop
reserve13,nan,nan,unitless,预留,difop
bot_sys_1v2,1.104,1.296,V,底板1.2V电压,difop
bot_vcco_psddr,1.012,1.188,V,底板片内监控电压1.1V,difop
reserve14,nan,nan,unitless,预留,difop
top_vbus,11,14,V,主板总输入电压,difop
top_sys_3v8,3.6,4.0,V,主板3.8V电压,difop
top_sys_3v3,3.1,3.5,V,主板3.3V电压,difop
top_sys_2v5,nan,nan,V,主板2.5V电压(A样暂不支持),difop
top_sys_1v1,1.0,1.2,V,主板1.1V电压,difop
top_rx_vbd,-25,-11,V,主板接收负压,difop
top_tx_charge,3.1,3.5,V,主板发射充能电压,difop
top_sys_1v8,1.62,1.98,V,主板1.8V电压,difop
top_sys_1v0,0.9,1.1,V,主板1.0V电压,difop
bot_psintlp,0.782,0.918,V,底板片内电压0.85V,difop
bot_psintfp,0.782,0.918,V,底板片内电压0.85V,difop
bot_ps_aux,1.656,1.944,V,底板片内电压1.8V,difop
bot_pl_vccint,0.782,0.918,V,底板片内电压0.85V,difop
reserve15,nan,nan,unitless,预留,difop
total_power,1,36,W,整机功率,difop