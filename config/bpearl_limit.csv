﻿# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
top_2V5,2.4,2.6,V,主板2.5V电压（VDC_TOP_2V5）
top_HV,-200,-100,V,主板负高压（VDC_TOP_HV）
top_N3V3,-3.6,-3.0,V,主板-3.3V电压（VDC_TOP_N3V3）
top_TX3V8,3.6,3.9,V,主板充能电压（VDC_TOP_TX3V8）
top_vbus,11,15.5,V,主板总输入电压（VDC_TOP_VBUS）
bot_12V,11,14,V,底板12V电压（VDC_BOT_12V）
bot_5V,4.8,5.5,V,底板5V电压（VDC_BOT_5V）
bot_fpga_1V,0.9,1.1,V,底板FPGA内核1V（VDC_BOT_FPGA_1V）
machine_vbus,9,36,V,整机输入电压（VDC_BOT_VBUS）
bot_curr,0.1,1,A,总输入电流（Current_BOT_VBUS）
top_fpga_temp,0,120,Celsius,主板fpga内核温度（Temp_TOP_FPGA）
bot_fpga_temp,0,120,Celsius,底板fpga内核温度（Temp_BOT_FPGA）
top_under_temp,0,110,Celsius,主板BOT温度（Temp_TOP_Under）
apd_temp,0,110,Celsius,APD温度值（Temp_APD）
real_speed,1190,1210,RPM,实时转速
stress_test,0,30,unitless,压力测试通过次数
upload_error_rate,0,0,unitless,上行误码率
download_error_rate,0,0,unitless,下行误码率