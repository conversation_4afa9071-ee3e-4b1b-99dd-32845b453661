﻿# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
apd_avg_temp,0,110,<PERSON><PERSON><PERSON>,接收板APD平均温度
top_under_temp,0,110,<PERSON><PERSON><PERSON>,主板底面温度
top_fpga_temp,0,120,<PERSON><PERSON><PERSON>,主板fpga温度
bot_fpga_temp,0,120,<PERSON><PERSON><PERSON>,底板fpga温度
top_5V,4.8,5.5,V,主板5V
top_NHV,-200,-100,V,主板HV
top_fpga_vol,0.9,1.1,V,主板fpga电压(1V0)
top_fpga_aux,1.7,1.9,V,主板fpga-aux(1V8)
bot_12V,11,14,V,底板 12V
bot_5V,4.8,5.5,V,底板 5V
bot_fpga_vccint,0.9,1.1,V,底板 FPGA vccint（1v）
bot_fpga_vccaux,1.7,1.9,V,底板 FPGA vccaux（1.8v）
cbus_curr,0.1,1.6,A,12V电流CBUS
real_speed,1190,1210,RPM,实时转速
stress_test,30,30,unitless,压力测试通过次数
upload_error_rate,0,0,unitless,上行误码率
download_error_rate,0,0,unitless,下行误码率