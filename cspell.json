// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON>sen",
    "Antialiasing",
    "appimage",
    "AUTOSAR",
    "Axisd",
    "bbft",
    "bbfv",
    "bottops",
    "Bpearl",
    "bugprone",
    "CALIB",
    "Cbus",
    "chardet",
    "CICD",
    "combobox",
    "cpack",
    "dialout",
    "DIFOP",
    "DRSFSCLOG",
    "dspinbox",
    "dtags",
    "duts",
    "dwntst",
    "Eigen",
    "Emissing",
    "Encod",
    "Fout",
    "FUNCSIG",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "gprmc",
    "GPTP",
    "groupbox",
    "gtest",
    "hhmmss",
    "hicpp",
    "intrin",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "libmems",
    "libqt",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "lsusb",
    "mainwindow",
    "Manu",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "METATYPE",
    "mseries",
    "MSOP",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "nqle",
    "opencv",
    "OPENMP",
    "pcap",
    "pcba",
    "Pixmap",
    "pktcnt",
    "psddr",
    "psintfp",
    "psintlp",
    "QMESSAGE",
    "qobject",
    "qreal",
    "qsetting",
    "qsettings",
    "Quaterniond",
    "refcal",
    "robosense",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "serialport",
    "setcap",
    "Shen",
    "SHIYAN",
    "Sout",
    "spdlog",
    "suteng",
    "tablewidget",
    "tabwidget",
    "thorlabs",
    "topboard",
    "toptobot",
    "tparam",
    "udev",
    "unitless",
    "uptst",
    "usbtmc",
    "usermod",
    "utest",
    "VBavg",
    "VBpeak",
    "vbus",
    "vccaux",
    "vccint",
    "vcco",
    "widgetaction",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "ZHONG"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
