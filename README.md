﻿# 雷达老化测试软件

## 1 需求背景及原理

### 1.1 背景

- 雷达在出厂前需要确保雷达性能,防止出货雷达存在质量问题.所以,需要增加雷达老化工位,让雷达长时间运行,并保持一定频率地获取雷达数据进行分析,要求在规定的老化时间内,雷达的各项数据必须无异常,方为老化成功.

### 1.2 原理

- 配置雷达相关寄存器,使雷达进入要求的运行状态.
- 读取雷达寄存器,并进行计算分析.
- 根据设定的寄存器数据上下限阈值,判断寄存器数据是否合格.
- 当存在任一项数据不合格时,判定为老化失败,需对雷达进行分析.

### 1.3 需求

- 支持对Helios,Ruby4,Bp4等雷达的老化.
- 至少支持同时老化31台雷达.
- 可对老化的雷达数量,老化时间可配置等.

## 2 软件依赖及安装运行

### 2.1 环境依赖

连通性测试软件软件运行在ubuntu 20.04环境下,且依赖以下模块，依赖版本及安装方式如下：

| 依赖软件                | 测试通过版本 | 安装方式      |
| ----------------------- | ------------ | ------------- |
| Qt                      | 5.12.8       | 系统自带      |
| mech_communication      | helios_ruby分支   | submodule添加 |
| relay_controller_driver | develop分支  | submodule添加 |
| rsfsc_lib               | v1.4.0分支   | submodule添加 |

### 2.2 编译运行

```
mkdir build 
cd build
cmake ..
make
./lidar_aging
```

## 3 寄存器表及阈值表

每个类型的雷达都有自己的寄存器表和阈值表,在工程目录的config文件夹下,其分类及文件名如下:

MEMS:

M1:m1_limit.csv, m1_register.csv.


以上文件,通过安装包安装路径为 /usr/local/share/lidar_mech_aging/config/.

在自动运行时,软件将会自动切换当前雷达对应的配置文件.
