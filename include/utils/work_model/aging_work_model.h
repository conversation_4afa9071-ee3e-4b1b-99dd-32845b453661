﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AGING_WORK_MODEL_H
#define AGING_WORK_MODEL_H

#include "data_struct.h"
#include "rsfsc_fsm/mech/mech_work_model.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QDir>
#include <condition_variable>
#include <widget_log_setting.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
class WorkModel0351;

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

class AgingWorkModel : public MechWorkModel
{
  friend WorkModel0351;

public:
  struct Path
  {
    QDir data_dir;
    QDir result_dir;
    QDir temp_dir;
    QString scale_file_name = "scale_data.csv";
    QString firmware_app_file_path;
    QString firmware_bot_file_path;
    QString firmware_top_file_path;
    QString firmware_config_file_path;
    QString connect_dump_file_path;
    QString exclude_msop_dump_file_path;
    QString only_msop_dump_file_path;
    QString chn_angle_file_path;
    QString vbd_file_path;
    QString verification_file_path;
  };

  struct EncodCalibData
  {
    std::vector<uint32_t> scale_avg;
    std::vector<uint32_t> calibration_scale_coeff;
    std::vector<uint32_t> angle_insert_step;
  };

  explicit AgingWorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info);
  AgingWorkModel(const AgingWorkModel&) = delete;
  AgingWorkModel(AgingWorkModel&&)      = delete;
  AgingWorkModel& operator=(const AgingWorkModel&) = delete;
  AgingWorkModel& operator=(AgingWorkModel&&) = delete;
  ~AgingWorkModel() override                  = default;
  void abort() override;
  int getLogIndex() { return getLidarIndex(); }
  rsfsc_lib::WidgetLogSetting* getWidgetLogSetting() override;
  void setParaInfo(const ParaInfo& _para_info) { para_info_ = _para_info; }
  ParaInfo& getParaInfo() { return para_info_; };

  void setNeedRestoreIp(const bool _is_need_restore_ip) { is_need_restore_ip_ = _is_need_restore_ip; }
  [[nodiscard]] bool getNeedRestoreIp() const { return is_need_restore_ip_; }

  void setAgingFinish(const bool _is_aging_finish) { is_aging_finish_ = _is_aging_finish; }
  [[nodiscard]] bool getAgingFinish() const { return is_aging_finish_; }

  template <typename String, typename... Args>
  void setFailLabel(String&& _fmt, Args&&... _args)
  {
    auto msg = fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...);
    LOG_INDEX_ERROR(msg);
    fail_label_ = QString::fromStdString(msg);
  }
  [[nodiscard]] const QString& getFailLabel() const { return fail_label_; }

  template <typename String, typename... Args>
  void setFailMsg(String&& _fmt, Args&&... _args)
  {
    // TODO: 添加一个参数，直接获取到一个默认的fsm，可以直接记录好extra_info就可以
    auto msg = fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...);
    LOG_INDEX_ERROR(msg);
    fail_msg_ = QString::fromStdString(msg);
  }
  [[nodiscard]] const QString& getFailMsg() const { return fail_msg_; }

  void setUpdateStateCb(std::function<void(const AgingState)> _update_aging_state_cb,
                        std::function<void(const StressState)> _update_stress_state_cb,
                        std::function<void(const FirmwareUpdateState)> _update_firmware_update_state_cb,
                        std::function<void(const EncodeCalibState)> _update_code_wheel_calib_state_cb,
                        std::function<void(const RunState)> _update_run_state_cb)
  {
    update_aging_state_cb_            = std::move(_update_aging_state_cb);
    update_stress_state_cb_           = std::move(_update_stress_state_cb);
    update_firmware_update_state_cb_  = std::move(_update_firmware_update_state_cb);
    update_code_wheel_calib_state_cb_ = std::move(_update_code_wheel_calib_state_cb);
    update_run_state_cb_              = std::move(_update_run_state_cb);
  }

  void resetAbort() override
  {
    is_need_restore_ip_ = false;
    is_aging_finish_    = false;
  }

  RunState getFsmState() { return fsm_state_; }
  void setFsmState(const RunState _state) { fsm_state_ = _state; }

  void resetEncodeCalibCount() { encode_calib_count_ = 0; }
  void addEncodeCalibCount() { encode_calib_count_++; }
  [[nodiscard]] int getEncodeCalibCount() const { return encode_calib_count_; }

  bool addMeasureMessage(const QString& _name, const bool _data);
  bool addMeasureMessage(const QString& _name, const double _data, const rsfsc_lib::MeasureDataType _data_type);
  bool addMeasureMessage(const QString& _name, const std::string& _data);
  bool addMeasureMessage(const DifopInfo& _difop_info);
  bool addMeasureMessage(const LimitInfo& _limit_info, const QVariant& _value);

  /**
   * @brief     通过寄存器csv读取单个寄存器数据
   * 
   * @param     _key               
   * @param     _data              
   * @param     _timeout           
   * @return    true               
   * @return    false              
  **/
  bool readRegDataByKey(const QString& _key, uint32_t& _data, const int _timeout = 8000);
  /**
   * @brief     通过寄存器csv读取顶板寄存器，可添加字节大小，自动拼接
   * 
   * @param     _key               
   * @param     _data              
   * @param     _timeout           
   * @return    true               
   * @return    false              
  **/
  bool readTopRegDataByKey(const QString& _key, uint32_t& _data, const uint32_t _byte_size, const int _timeout = 8000);

  bool writeRegDataByKey(const QString& _key, const uint32_t _data);
  bool writeRegDataByKeyWithVer(const QString& _key, const uint32_t _data);
  bool writeCsvData(const QString& _key);

  bool readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2);
  bool readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex);

  void notifyDataCheck();
  bool waitForDataCheck();

  bool turnOnRelay();
  bool turnOffRelay();
  bool turnRelay(const bool _is_open);
  bool toggleRelay(const int _interval = 3000);

  bool rebootAndWait();

  bool checkAllState();

  bool initPath(const Path& _path);
  void updateFirmwareUpdateState(const FirmwareUpdateState _state);
  [[nodiscard]] FirmwareUpdateState getFirmwareUpdateState() const;
  void updateEncodeCalibState(const EncodeCalibState _state);
  [[nodiscard]] EncodeCalibState getEncodeCalibState() const;
  void updateAgingState(const AgingState _state);
  [[nodiscard]] AgingState getAgingState() const;
  void updateStressState(const StressState _state);
  [[nodiscard]] StressState getStressState() const;
  void updateRunState(const RunState _state);
  void updateRunState();
  [[nodiscard]] RunState getRunState() const;

  bool unzipFirmware(QString _file_dir = "");
  bool findAndSetPath(const QString& _folder, const QStringList& _file_list, const QString& _keyword, QString& _path);
  QString getFirmwareZipPath(const QString& _md5);
  std::map<QString, QString> getZipMd5(QString _dir_path = "");
  bool checkFirmware(QString _file_path = "");
  bool updateFirmwareApp(QString _file_path = "");
  bool updateFirmwareBot(QString _file_path = "");
  bool updateFirmwareTop(QString _file_path = "");
  bool updateFirmwareWriteConfig(QString _file_path = "");
  bool updateFirmwareCheck(QString _file_path = "");
  bool checkBeforeFirmwareUpdate();

  virtual bool clearCalibData();
  bool writeExpZeroAngle();

  virtual bool checkAgingData();
  bool checkAgingData(const DifopInfo& _difop_info);
  bool checkMsopData(const std::vector<mech::MsopPacket>& _msop_pkt_vec);
  int getPastDifopTime();
  [[nodiscard]] DifopInfo getLastDifopInfo() const;
  virtual bool addDifopMonitorResult();
  virtual bool appendAgingData();
  std::vector<mech::MsopPacket> getMsopData(const int _count, const int _timeout = 60000);

  // require vertical angle from mes
  bool requireVerAngleDataBySubSN();
  bool requireVerAngleData();
  // both ver and hor
  bool writeChnAngleData();

  bool requiredVbdData();
  bool readVbdCurveAndCalData();
  std::vector<uint8_t> generateVbdBit();
  virtual bool writeVbdData();
  bool checkVbdData();

  // 获取mac地址
  bool requireCustomerInfo();

  bool changeLidarNet();
  bool restoreLidarNet();

  bool saveEncodeCalibData(const std::vector<std::vector<uint32_t>>& _data);
  std::vector<std::vector<uint32_t>> loadEncodeCalibData();
  bool processEncodeCalibData(const std::vector<std::vector<uint32_t>>& _data, EncodCalibData& _encod_calib_data);
  bool saveEncodeCalibData(const EncodCalibData& _encod_calib_data, const QString& _file_name_suffix = "");
  bool checkEncodeCalibData(const EncodCalibData& _encod_calib_data);

  bool checkWithLimit(const LimitInfo& _limit_info, const QVariant& _value);
  static QString dataToString(const QVariant& _value);
  static QString dataToString(const QVariant& _value, const QVariant::Type& _type);

  virtual bool humanConfirm();

  void updateStressCount(const int _count);

  [[nodiscard]] const Path& getPath() const { return path_; }

  bool startTcpdumpBothOrgAndObjIPExcludeMSOP();
  bool startTcpdumpExcludeMSOP();
  bool startTcpdumpOnlyMSOP();
  void stopTcpdumpExcludeMSOP();
  void stopTcpdumpOnlyMSOP();

private:
  Path path_;

  FirmwareUpdateState firmware_update_state_ = FIRMWARE_UPDATE_IDLE;
  EncodeCalibState encode_calib_state_       = ENCODE_CALIB_IDLE;
  AgingState aging_state_                    = AGING_IDLE;
  StressState stress_state_                  = STRESS_IDLE;
  RunState run_state_                        = RUN_IDLE;

  RunState fsm_state_ = RUN_IDLE;

  std::function<void(const AgingState)> update_aging_state_cb_;
  std::function<void(const StressState)> update_stress_state_cb_;
  std::function<void(const FirmwareUpdateState)> update_firmware_update_state_cb_;
  std::function<void(const EncodeCalibState)> update_code_wheel_calib_state_cb_;
  std::function<void(const RunState)> update_run_state_cb_;
  QDateTime last_difop_time_;
  mech::DifopPacket last_difop_pkt_;

  ParaInfo para_info_;

  // lidar 状态
  double optical_up_error_rate_   = NAN;
  double optical_down_error_rate_ = NAN;

  bool is_need_restore_ip_ = false;
  bool is_aging_finish_    = false;

  int encode_calib_count_ = 0;

  QString fail_label_;
  QString fail_msg_;

  // 固件升级
  QString firmware_md5_;
  QString firmware_version_;
  QString mes_mac_address_;
  uint32_t mes_top_version_    = 0;
  uint32_t mes_bot_version_    = 0;
  uint32_t mes_app_version_    = 0;
  uint32_t mes_motor_version_  = 0;
  uint32_t mes_config_version_ = 0;

  std::mutex mutex_data_check_;
  std::condition_variable cv_data_check_;
  bool is_data_check_ = false;
  std::shared_ptr<CsvUtils> sim_angle_csv_utils_ptr_;
  std::shared_ptr<CsvUtils> exp_angle_csv_utils_ptr_;
  std::vector<float> hor_angle_vec_;
  std::vector<float> ver_angle_vec_;
  std::vector<float> sim_ver_angle_vec_;
  std::vector<float> exp_ver_angle_vec_;

  // vbd标定
  float vbd_voltage_          = 0.0F;
  uint32_t vbd_intercept_hex_ = 0;
  uint32_t vbd_err_hex_       = 0;
  float vbd_cal_k_            = 0.0F;
  float exp_zero_angle_       = 0.0F;
};

}  // namespace lidar
}  // namespace robosense
#endif  // AGING_WORK_MODEL_H