﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef WORK_MODEL_0351_H
#define WORK_MODEL_0351_H

#include "aging_work_model.h"
#include "mech_communication/protocol/data_struct/airy_pld.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class WorkModel0351 : public AgingWorkModel
{
public:
  explicit WorkModel0351(rsfsc_lib::WidgetLidarInfo* _lidar_info) :
    AgingWorkModel(_lidar_info), last_airy_pld_difop_pkt_ {}
  {
    setupAiryPldDifopCallback();
  }
  WorkModel0351(const WorkModel0351&) = delete;
  WorkModel0351(WorkModel0351&&)      = delete;
  WorkModel0351& operator=(const WorkModel0351&) = delete;
  WorkModel0351& operator=(WorkModel0351&&) = delete;
  ~WorkModel0351() override                 = default;

  bool humanConfirm() override;

  bool writeVbdData() override;
  bool clearCalibData() override;

protected:
  bool checkAgingData() override;
  bool appendAgingData() override;
  bool addDifopMonitorResult() override;

  // 引入基类的addMeasureMessage重载版本
  using AgingWorkModel::addMeasureMessage;
  bool addMeasureMessage(const airy_pld::DifopInfo& _airy_pld_difop_info);

  [[nodiscard]] airy_pld::DifopInfo getLastAiryPldDifopInfo() const;
  void setupAiryPldDifopCallback();

private:
  airy_pld::DifopPacket last_airy_pld_difop_pkt_;
  QDateTime last_airy_pld_difop_time_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // WORK_MODEL_0351_H