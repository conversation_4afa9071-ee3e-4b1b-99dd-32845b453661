﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef CRC_UTILS_H
#define CRC_UTILS_H
#include <cstdint>
#include <vector>

// std::vector<int> intToBits(int num, int bitSize)
// {
//   std::vector<int> bits(bitSize, 0);
//   for (int i = 0; i < bitSize; ++i)
//   {
//     bits[bitSize - 1 - i] = (num >> i) & 1;
//   }
//   return bits;
// }

// // 将二进制位向量转换回整数
// int bitsToInt(const std::vector<int>& bits)
// {
//   int num = 0;
//   for (size_t i = 0; i < bits.size(); ++i)
//   {
//     num |= (bits[i] << (bits.size() - 1 - i));
//   }
//   return num;
// }

// // nextCRC16_D8 实现
// uint16_t nextCRC16(uint8_t data_in, uint16_t crc_i)
// {
//   // 将数据和 CRC 转换为二进制位向量
//   std::vector<int> crc_bits  = intToBits(crc_i, 16);
//   std::vector<int> data_bits = intToBits(data_in, 8);

//   // 初始化 LFSR 寄存器
//   std::vector<int> lfsr_c(16, 0);

//   // 计算 LFSR 反馈
//   lfsr_c[0] = crc_bits[8] ^ crc_bits[9] ^ crc_bits[10] ^ crc_bits[11] ^ crc_bits[12] ^ crc_bits[13] ^ crc_bits[14] ^
//               crc_bits[15] ^ data_bits[0] ^ data_bits[1] ^ data_bits[2] ^ data_bits[3] ^ data_bits[4] ^ data_bits[5] ^
//               data_bits[6] ^ data_bits[7];
//   lfsr_c[1] = crc_bits[9] ^ crc_bits[10] ^ crc_bits[11] ^ crc_bits[12] ^ crc_bits[13] ^ crc_bits[14] ^ crc_bits[15] ^
//               data_bits[1] ^ data_bits[2] ^ data_bits[3] ^ data_bits[4] ^ data_bits[5] ^ data_bits[6] ^ data_bits[7];
//   lfsr_c[2]  = crc_bits[8] ^ crc_bits[9] ^ data_bits[0] ^ data_bits[1];
//   lfsr_c[3]  = crc_bits[9] ^ crc_bits[10] ^ data_bits[1] ^ data_bits[2];
//   lfsr_c[4]  = crc_bits[10] ^ crc_bits[11] ^ data_bits[2] ^ data_bits[3];
//   lfsr_c[5]  = crc_bits[11] ^ crc_bits[12] ^ data_bits[3] ^ data_bits[4];
//   lfsr_c[6]  = crc_bits[12] ^ crc_bits[13] ^ data_bits[4] ^ data_bits[5];
//   lfsr_c[7]  = crc_bits[13] ^ crc_bits[14] ^ data_bits[5] ^ data_bits[6];
//   lfsr_c[8]  = crc_bits[0] ^ crc_bits[14] ^ crc_bits[15] ^ data_bits[6] ^ data_bits[7];
//   lfsr_c[9]  = crc_bits[1] ^ crc_bits[15] ^ data_bits[7];
//   lfsr_c[10] = crc_bits[2];
//   lfsr_c[11] = crc_bits[3];
//   lfsr_c[12] = crc_bits[4];
//   lfsr_c[13] = crc_bits[5];
//   lfsr_c[14] = crc_bits[6];
//   lfsr_c[15] = crc_bits[7] ^ crc_bits[8] ^ crc_bits[9] ^ crc_bits[10] ^ crc_bits[11] ^ crc_bits[12] ^ crc_bits[13] ^
//                crc_bits[14] ^ crc_bits[15] ^ data_bits[0] ^ data_bits[1] ^ data_bits[2] ^ data_bits[3] ^ data_bits[4] ^
//                data_bits[5] ^ data_bits[6] ^ data_bits[7];

//   // 将 LFSR 寄存器的位转换回整数并返回
//   return bitsToInt(lfsr_c);
// }
// uint16_t calculateCrc(const uint8_t* crc_data_in, size_t len)
// {
//   uint16_t crc_i = 0xFFFF;  // 初始值
//   for (size_t i = 0; i < len; ++i)
//   {
//     // NOLINTNEXTLINE(cppcoreguidelines-pro-bounds-pointer-arithmetic)
//     crc_i = nextCRC16(crc_data_in[i], crc_i);
//   }
//   // 最终的 CRC 值需要与 0xFFFF 异或
//   return crc_i ^ 0xFFFF;
// }
// uint16_t calculateCrc(const char* crc_data_in, size_t len)
// {
//   return calculateCrc(reinterpret_cast<const uint8_t*>(crc_data_in), len);
// }

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
class CrcUtils
{
public:
  explicit CrcUtils();
  CrcUtils(CrcUtils&&)      = default;
  CrcUtils(const CrcUtils&) = default;
  CrcUtils& operator=(CrcUtils&&) = default;
  CrcUtils& operator=(const CrcUtils&) = default;
  ~CrcUtils();

  static uint16_t calculateCrc(const uint8_t* _crc_data_in, size_t _len);
  static uint16_t calcCrc(const char* _crc_data_in, size_t _len);
  static uint16_t nextCRC16(uint8_t _data_in, uint16_t _crc_i);

private:
  static std::vector<int> intToBits(int _num, int _bit_size);
  static int bitsToInt(const std::vector<int>& _bits);
};

}  // namespace lidar
}  // namespace robosense
#endif  // CRC_UTILS_H