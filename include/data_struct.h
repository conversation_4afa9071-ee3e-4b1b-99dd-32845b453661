﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "mech_communication/protocol/data_struct/airy_pld.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QHostAddress>
#include <QObject>
#include <QString>
#include <QVariant>
#include <qdatetime.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
enum class ActionState
{
  CHECK_MES,                     // 检查MES
  AGING_CONNECT_LIDAR,           // 老化连接雷达
  FIRMWARE_UPDATE_UNZIP,         // 固件升级解压文件
  FIRMWARE_UPDATE_APP,           // 固件升级APP
  FIRMWARE_UPDATE_BOT,           // 固件升级底板
  FIRMWARE_UPDATE_TOP,           // 固件升级顶板
  FIRMWARE_UPDATE_WRITE_CONFIG,  // 固件升级写入配置
  FIRMWARE_UPDATE_REBOOT,        // 固件升级重启
  FIRMWARE_UPDATE_CHECK,         // 固件升级校验
  CLEAR_CALIB_DATA,              // 清除标定数据
  CHANGE_LIDAR_IP,               // 修改雷达IP
  ENCODING_CALIB_MOTOR_STABLE,   // 码盘标定就绪，等待电机稳定
  ENCODING_CALIB_COLLECT,        // 码盘标定采集数据
  ENCODING_CALIB_PROCESS_WRITE,  // 码盘标定计算
  ENCODING_CALIB_WRITE,          // 码盘标定写入寄存器固化
  CHN_ANGLE_WRITE,               // 角度写入
  VBD_CALIB,                     // VBD标定
  INIT_LIDAR,                    // 初始化雷达
  INIT_READ_DATA,                // 读取数据前的初始化
  READ_DATA,                     // 读取数据并校验检查
  DEINIT_READ_DATA,              // 读取数据后的反初始化
  STRESS_TEST,                   // 压测
  RESTORE_LIDAR_IP,              // 恢复雷达IP
  COOLING,                       // 冷却
  FAIL,                          // 失败
  ABORT,                         // 中断
  SUCCESS,                       // 成功
  FINAL,                         // 结束
  END = -1
};
enum EncodeCalibState
{
  ENCODE_CALIB_IDLE,
  ENCODE_CALIB_WAIT,
  ENCODE_CALIB_BUSY,
  ENCODE_CALIB_COLLECT,
  ENCODE_CALIB_PASS,
  ENCODE_CALIB_NG,
  ENCODE_CALIB_SKIP,
  ENCODE_CALIB_ABORT
};
enum FirmwareUpdateState
{
  FIRMWARE_UPDATE_IDLE,
  FIRMWARE_UPDATE_WAIT,
  FIRMWARE_UPDATE_BUSY,
  FIRMWARE_UPDATE_UNZIP,
  FIRMWARE_UPDATE_APP,
  FIRMWARE_UPDATE_BOT,
  FIRMWARE_UPDATE_TOP,
  FIRMWARE_UPDATE_WRITE_CONFIG,
  FIRMWARE_UPDATE_REBOOT,
  FIRMWARE_UPDATE_CHECK,
  FIRMWARE_UPDATE_PASS,
  FIRMWARE_UPDATE_NG,
  FIRMWARE_UPDATE_SKIP,
  FIRMWARE_UPDATE_ABORT
};
enum AgingState
{
  AGING_IDLE,
  AGING_WAIT,
  AGING_BUSY,
  AGING_COOLING,
  AGING_PASS,
  AGING_NG,
  AGING_SKIP,
  AGING_ABORT
};
enum StressState
{
  STRESS_IDLE,
  STRESS_WAIT,
  STRESS_BUSY,
  STRESS_PASS,
  STRESS_NG,
  STRESS_SKIP,
  STRESS_ABORT
};

enum RunState
{
  RUN_IDLE,
  RUN_BUSY,
  RUN_PASS,
  RUN_NG,
  RUN_ABORT
};

enum class ConnectionStatus
{
  UNINITIALIZED,
  CONNECTED,
  DISCONNECTED,
  LAGGING
};

struct ParaInfo
{
  int lidar_index;
  int row_num;
  int col_num;

  QString org_ip     = "*************";
  int org_msop_port  = 6699;
  int org_difop_port = 7788;

  bool fsm_change_ip;
  bool fsm_firmware_update;
  bool fsm_clear_calib_data;
  bool fsm_encoding_calib;
  bool fsm_chn_angle_write;
  bool fsm_vbd_calib;
  bool fsm_stress_test;
  bool fsm_aging;

  QString firmware_dir;
  bool is_use_zip_file;
  QString firmware_zip_file_path;

  int ping_timeout;
  int start_up_max_time;

  bool is_maintain_fail_env;

  // aging para
  int aging_time_secs;
  int aging_check_interval;
  int difop_timeout;
  int cooling_time_secs;

  // 监控msop数量
  int check_msop_size;

  // 码盘标定参数
  int encod_calib_collect_num;

  // VBD标定参数
  float vbd_cal_k;

  // 需要压测的次数
  int stress_num;
  // 压测重新上电时间
  int stress_toggle_interval;
  // 压测上电后等待的时间
  int stress_wait_start_time;
  // // 压测时候雷达转速
  // int stress_motor_speed;
  // 压测允许失败
  bool stress_allow_fail;
};

constexpr uint32_t ADDR_COEFF     = 0x83c05000;
constexpr uint32_t ADDR_STEP      = 0x83c05400;
constexpr uint32_t ADDR_INFO_LOCK = 0x83c04300;
constexpr uint32_t ADDR_SCALE     = 0x83c05800;
constexpr size_t ENCODE_NUM       = 99;

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_PROPERTY(TYPE, NAME, FUNC_NAME)                                  \
  Q_PROPERTY(TYPE NAME READ get##FUNC_NAME WRITE set##FUNC_NAME)                 \
  Q_PROPERTY(QVariant NAME##type READ get##FUNC_NAME##Type)                      \
public:                                                                          \
  TYPE get##FUNC_NAME() const { return NAME##_; }                                \
  void set##FUNC_NAME(const TYPE& _value) { (NAME##_) = _value; }                \
                                                                                 \
  QVariant get##FUNC_NAME##Type() const { return QVariant::fromValue(NAME##_); } \
                                                                                 \
private:                                                                         \
  TYPE NAME##_

class DifopInfo : public QObject
{
  Q_OBJECT
public:
  explicit DifopInfo(const mech::DifopPacket& _difop_pkt = mech::DifopPacket(),
                     const QDateTime& _date_time         = QDateTime::currentDateTime(),
                     QObject* _parent                    = nullptr) :
    QObject(_parent)
  {
    setDateTime(_date_time);
    setDifopPacket(_difop_pkt);
  };
  DifopInfo(const DifopInfo&) = delete;
  DifopInfo(DifopInfo&&)      = delete;
  DifopInfo& operator=(const DifopInfo&) = delete;
  DifopInfo& operator=(DifopInfo&&) = delete;
  ~DifopInfo() override             = default;

  DECLARE_PROPERTY(QString, sn, Sn);
  DECLARE_PROPERTY(int, motor_set_speed, MotorSetSpeed) {};
  DECLARE_PROPERTY(QString, ip_src, IpSrc);
  DECLARE_PROPERTY(QString, ip_dst, IpDst);
  DECLARE_PROPERTY(QString, mac_addr, MacAddr);
  DECLARE_PROPERTY(int, msop_port, MsopPort) {};
  DECLARE_PROPERTY(int, difop_port, DifopPort) {};
  DECLARE_PROPERTY(int, fov_start, FovStart) {};
  DECLARE_PROPERTY(int, fov_end, FovEnd) {};
  DECLARE_PROPERTY(int, lock_phase, LockPhase) {};
  DECLARE_PROPERTY(uint32_t, top_firmware_version, TopFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, bot_firmware_version, BotFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, app_firmware_version, AppFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, motor_firmware_version, MotorFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, cgi_firmware_version, CgiFirmwareVersion) {};
  DECLARE_PROPERTY(int, gps_baud_rate, GpsBaudRate) {};
  DECLARE_PROPERTY(uint64_t, time_sec, TimeSec) {};
  DECLARE_PROPERTY(uint32_t, time_nano, TimeNano) {};
  DECLARE_PROPERTY(int, time_sync_status, TimeSyncStatus) {};
  DECLARE_PROPERTY(int, motor_dir, MotorDir) {};
  DECLARE_PROPERTY(uint32_t, total_run_time, TotalRunTime) {};
  DECLARE_PROPERTY(int, reboot_count, RebootCount) {};
  DECLARE_PROPERTY(int, pps_lock, PpsLock) {};
  DECLARE_PROPERTY(int, gprmc_lock, GprmcLock) {};
  DECLARE_PROPERTY(int, utc_lock, UtcLock) {};
  DECLARE_PROPERTY(int, gprmc_input, GprmcInput) {};
  DECLARE_PROPERTY(int, pps_input, PpsInput) {};
  DECLARE_PROPERTY(int, realtime_phase, RealtimePhase) {};
  DECLARE_PROPERTY(int, realtime_speed, RealtimeSpeed) {};
  DECLARE_PROPERTY(uint32_t, start_time, StartTime) {};
  DECLARE_PROPERTY(float, top_input_vol, TopInputVol) {};
  DECLARE_PROPERTY(float, top_3v8, Top3v8) {};
  DECLARE_PROPERTY(float, top_3v3, Top3v3) {};
  DECLARE_PROPERTY(float, top_1v1, Top1v1) {};
  DECLARE_PROPERTY(float, top_neg_vol, TopNegVol) {};
  DECLARE_PROPERTY(float, top_3v3_rx, Top3v3Rx) {};
  DECLARE_PROPERTY(float, top_charge_vol, TopChargeVol) {};
  DECLARE_PROPERTY(float, total_input_vol, TotalInputVol) {};
  DECLARE_PROPERTY(float, bot_12v, Bot12v) {};
  DECLARE_PROPERTY(float, bot_mcu_0v85, BotMcu0v85) {};
  DECLARE_PROPERTY(float, bot_fpga_1v, BotFpga1v) {};
  DECLARE_PROPERTY(float, total_input_cur, TotalInputCur) {};
  DECLARE_PROPERTY(float, top_fpga_temp, TopFpgaTemp) {};
  DECLARE_PROPERTY(float, top_tx_temp, TopTxTemp) {};
  DECLARE_PROPERTY(float, top_rx_459_temp_n, TopRx459TempN) {};
  DECLARE_PROPERTY(float, top_rx_459_temp_p, TopRx459TempP) {};
  DECLARE_PROPERTY(float, bot_imu_temp, BotImuTemp) {};
  DECLARE_PROPERTY(float, bot_fpga_temp, BotFpgaTemp) {};
  DECLARE_PROPERTY(float, total_power, TotalPower) {};

public:
  void setDifopPacket(const mech::DifopPacket& _difop_pkt)
  {
    sn_                     = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.sn, "")));
    motor_set_speed_        = _difop_pkt.motor_set_speed;
    ip_src_                 = QHostAddress(_difop_pkt.ip_src).toString();
    ip_dst_                 = QHostAddress(_difop_pkt.ip_dst).toString();
    mac_addr_               = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.mac_addr, ":")));
    msop_port_              = _difop_pkt.msop_port;
    difop_port_             = _difop_pkt.difop_port;
    top_firmware_version_   = _difop_pkt.top_firmware_version;
    bot_firmware_version_   = _difop_pkt.bot_firmware_version;
    app_firmware_version_   = _difop_pkt.app_firmware_version;
    motor_firmware_version_ = _difop_pkt.motor_firmware_version;
    cgi_firmware_version_   = _difop_pkt.cgi_firmware_version;
    gps_baud_rate_          = static_cast<int>(_difop_pkt.gps_baud_rate);
    // time_sec_               = _difop_pkt.time_sec;
    time_nano_         = _difop_pkt.time_nano;
    time_sync_status_  = _difop_pkt.time_sync_status;
    motor_dir_         = _difop_pkt.motor_dir;
    total_run_time_    = _difop_pkt.total_run_time;
    reboot_count_      = _difop_pkt.reboot_count;
    pps_lock_          = _difop_pkt.gps_status.pps_lock;
    gprmc_lock_        = _difop_pkt.gps_status.gprmc_lock;
    utc_lock_          = _difop_pkt.gps_status.utc_lock;
    gprmc_input_       = _difop_pkt.gps_status.gprmc_input;
    pps_input_         = _difop_pkt.gps_status.pps_input;
    realtime_phase_    = _difop_pkt.realtime_phase;
    realtime_speed_    = _difop_pkt.realtime_speed;
    start_time_        = _difop_pkt.start_time;
    top_input_vol_     = static_cast<float>(_difop_pkt.top_input_vol / 100.0);
    top_3v8_           = static_cast<float>(_difop_pkt.top_3v8 / 100.0);
    top_3v3_           = static_cast<float>(_difop_pkt.top_3v3 / 100.0);
    top_1v1_           = static_cast<float>(_difop_pkt.top_1v1 / 100.0);
    top_neg_vol_       = static_cast<float>(_difop_pkt.top_neg_vol / 100.0);
    top_3v3_rx_        = static_cast<float>(_difop_pkt.top_3v3_rx / 100.0);
    top_charge_vol_    = static_cast<float>(_difop_pkt.top_charge_vol / 100.0);
    total_input_vol_   = static_cast<float>(_difop_pkt.total_input_vol / 100.0);
    bot_12v_           = static_cast<float>(_difop_pkt.bot_12v / 100.0);
    bot_mcu_0v85_      = static_cast<float>(_difop_pkt.bot_mcu_0v85 / 100.0);
    bot_fpga_1v_       = static_cast<float>(_difop_pkt.bot_fpga_1v / 100.0);
    total_input_cur_   = static_cast<float>(_difop_pkt.total_input_cur / 100.0);
    top_fpga_temp_     = static_cast<float>(_difop_pkt.top_fpga_temp / 100.0);
    top_tx_temp_       = static_cast<float>(_difop_pkt.top_tx_temp / 100.0);
    top_rx_459_temp_n_ = static_cast<float>(_difop_pkt.top_rx_459_temp_n / 100.0);
    top_rx_459_temp_p_ = static_cast<float>(_difop_pkt.top_rx_459_temp_p / 100.0);
    bot_imu_temp_      = static_cast<float>(_difop_pkt.bot_imu_temp / 100.0);
    bot_fpga_temp_     = static_cast<float>(_difop_pkt.bot_fpga_temp / 100.0);
    total_power_       = static_cast<float>(_difop_pkt.total_power / 100.0);
  }

  [[nodiscard]] QDateTime getDateTime() const { return datetime_; }
  void setDateTime(const QDateTime& _datetime) { datetime_ = _datetime; }

  [[nodiscard]] bool isValid() const { return datetime_.isValid(); }

private:
  QDateTime datetime_;
};

namespace airy_pld
{
class DifopInfo : public QObject
{
  Q_OBJECT
public:
  explicit DifopInfo(const airy_pld::DifopPacket& _difop_pkt = airy_pld::DifopPacket(),
                     const QDateTime& _date_time             = QDateTime::currentDateTime(),
                     QObject* _parent                        = nullptr) :
    QObject(_parent)
  {
    setDateTime(_date_time);
    setDifopPacket(_difop_pkt);
  };
  DifopInfo(const DifopInfo&) = delete;
  DifopInfo(DifopInfo&&)      = delete;
  DifopInfo& operator=(const DifopInfo&) = delete;
  DifopInfo& operator=(DifopInfo&&) = delete;
  ~DifopInfo() override             = default;

  DECLARE_PROPERTY(QString, sn, Sn);
  DECLARE_PROPERTY(int, motor_set_speed, MotorSetSpeed) {};
  DECLARE_PROPERTY(QString, ip_src, IpSrc);
  DECLARE_PROPERTY(QString, ip_dst, IpDst);
  DECLARE_PROPERTY(QString, mac_addr, MacAddr);
  DECLARE_PROPERTY(int, msop_port, MsopPort) {};
  DECLARE_PROPERTY(int, difop_port, DifopPort) {};
  DECLARE_PROPERTY(int, fov_start, FovStart) {};
  DECLARE_PROPERTY(int, fov_end, FovEnd) {};
  DECLARE_PROPERTY(int, lock_phase, LockPhase) {};
  DECLARE_PROPERTY(uint32_t, top_firmware_version, TopFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, bot_firmware_version, BotFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, app_firmware_version, AppFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, motor_firmware_version, MotorFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, cgi_firmware_version, CgiFirmwareVersion) {};
  DECLARE_PROPERTY(int, gps_baud_rate, GpsBaudRate) {};
  DECLARE_PROPERTY(uint64_t, time_sec, TimeSec) {};
  DECLARE_PROPERTY(uint32_t, time_nano, TimeNano) {};
  DECLARE_PROPERTY(int, time_sync_status, TimeSyncStatus) {};
  DECLARE_PROPERTY(int, motor_dir, MotorDir) {};
  DECLARE_PROPERTY(uint32_t, total_run_time, TotalRunTime) {};
  DECLARE_PROPERTY(int, reboot_count, RebootCount) {};
  DECLARE_PROPERTY(int, pps_lock, PpsLock) {};
  DECLARE_PROPERTY(int, gprmc_lock, GprmcLock) {};
  DECLARE_PROPERTY(int, utc_lock, UtcLock) {};
  DECLARE_PROPERTY(int, gprmc_input, GprmcInput) {};
  DECLARE_PROPERTY(int, pps_input, PpsInput) {};
  DECLARE_PROPERTY(int, realtime_phase, RealtimePhase) {};
  DECLARE_PROPERTY(int, realtime_speed, RealtimeSpeed) {};
  DECLARE_PROPERTY(uint32_t, start_time, StartTime) {};
  DECLARE_PROPERTY(float, bot_vbus, BotVbus) {};
  DECLARE_PROPERTY(float, bot_ibus, BotIbus) {};
  DECLARE_PROPERTY(float, bot_sys_5v, BotSys5v) {};
  DECLARE_PROPERTY(float, bot_sys_12v, BotSys12v) {};
  DECLARE_PROPERTY(float, bot_vcco_psio0, BotVccoPsio0) {};
  DECLARE_PROPERTY(float, bot_sys_1v2, BotSys1v2) {};
  DECLARE_PROPERTY(float, bot_vcco_psddr, BotVccoPsddr) {};
  DECLARE_PROPERTY(float, top_vbus, TopVbus) {};
  DECLARE_PROPERTY(float, top_sys_3v8, TopSys3v8) {};
  DECLARE_PROPERTY(float, top_sys_3v3, TopSys3v3) {};
  DECLARE_PROPERTY(float, top_sys_2v5, TopSys2v5) {};
  DECLARE_PROPERTY(float, top_sys_1v1, TopSys1v1) {};
  DECLARE_PROPERTY(float, top_rx_vbd, TopRxVbd) {};
  DECLARE_PROPERTY(float, top_tx_charge, TopTxCharge) {};
  DECLARE_PROPERTY(float, top_sys_1v8, TopSys1v8) {};
  DECLARE_PROPERTY(float, top_sys_1v0, TopSys1v0) {};
  DECLARE_PROPERTY(float, bot_psintlp, BotPsintlp) {};
  DECLARE_PROPERTY(float, bot_psintfp, BotPsintfp) {};
  DECLARE_PROPERTY(float, bot_ps_aux, BotPsAux) {};
  DECLARE_PROPERTY(float, bot_pl_vccint, BotPlVccint) {};
  DECLARE_PROPERTY(float, total_power, TotalPower) {};
  DECLARE_PROPERTY(float, imu_qx, ImuQx) {};
  DECLARE_PROPERTY(float, imu_qy, ImuQy) {};
  DECLARE_PROPERTY(float, imu_qz, ImuQz) {};
  DECLARE_PROPERTY(float, imu_qw, ImuQw) {};
  DECLARE_PROPERTY(float, imu_x, ImuX) {};
  DECLARE_PROPERTY(float, imu_y, ImuY) {};
  DECLARE_PROPERTY(float, imu_z, ImuZ) {};
  DECLARE_PROPERTY(float, bot_soc_temp, BotSocTemp) {};
  DECLARE_PROPERTY(float, bot_fpd_temp, BotFpdTemp) {};
  DECLARE_PROPERTY(float, bot_lpd_temp, BotLpdTemp) {};
  DECLARE_PROPERTY(float, bot_imu_temp, BotImuTemp) {};
  DECLARE_PROPERTY(float, top_fpga_temp, TopFpgaTemp) {};
  DECLARE_PROPERTY(float, top_tx_temp, TopTxTemp) {};
  DECLARE_PROPERTY(float, top_rx_n_temp, TopRxNTemp) {};
  DECLARE_PROPERTY(float, top_rx_p_temp, TopRxPTemp) {};

public:
  void setDifopPacket(const airy_pld::DifopPacket& _difop_pkt)
  {
    sn_                     = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.sn, "")));
    motor_set_speed_        = _difop_pkt.motor_set_speed;
    ip_src_                 = QHostAddress(_difop_pkt.ip_src).toString();
    ip_dst_                 = QHostAddress(_difop_pkt.ip_dst).toString();
    mac_addr_               = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.mac_addr, ":")));
    msop_port_              = _difop_pkt.msop_port;
    difop_port_             = _difop_pkt.difop_port;
    fov_start_              = _difop_pkt.fov_start;
    fov_end_                = _difop_pkt.fov_end;
    lock_phase_             = _difop_pkt.lock_phase;
    top_firmware_version_   = _difop_pkt.top_firmware_version;
    bot_firmware_version_   = _difop_pkt.bot_firmware_version;
    app_firmware_version_   = _difop_pkt.app_firmware_version;
    motor_firmware_version_ = _difop_pkt.motor_firmware_version;
    cgi_firmware_version_   = _difop_pkt.cgi_firmware_version;
    gps_baud_rate_          = static_cast<int>(_difop_pkt.gps_baud_rate);
    time_nano_              = _difop_pkt.time_nano;
    time_sync_status_       = _difop_pkt.time_sync_status;
    motor_dir_              = _difop_pkt.motor_dir;
    total_run_time_         = _difop_pkt.total_run_time;
    reboot_count_           = _difop_pkt.reboot_count;
    pps_lock_               = _difop_pkt.gps_status.pps_lock;
    gprmc_lock_             = _difop_pkt.gps_status.gprmc_lock;
    utc_lock_               = _difop_pkt.gps_status.utc_lock;
    gprmc_input_            = _difop_pkt.gps_status.gprmc_input;
    pps_input_              = _difop_pkt.gps_status.pps_input;
    realtime_phase_         = _difop_pkt.realtime_phase;
    realtime_speed_         = _difop_pkt.realtime_speed;
    start_time_             = _difop_pkt.start_time;
    bot_vbus_               = static_cast<float>(_difop_pkt.bot_vbus / 100.0);
    bot_ibus_               = static_cast<float>(_difop_pkt.bot_ibus / 100.0);
    bot_sys_5v_             = static_cast<float>(_difop_pkt.bot_sys_5v / 100.0);
    bot_sys_12v_            = static_cast<float>(_difop_pkt.bot_sys_12v / 100.0);
    bot_vcco_psio0_         = static_cast<float>(_difop_pkt.bot_vcco_psio0 / 100.0);
    bot_sys_1v2_            = static_cast<float>(_difop_pkt.bot_sys_1v2 / 100.0);
    bot_vcco_psddr_         = static_cast<float>(_difop_pkt.bot_vcco_psddr / 100.0);
    top_vbus_               = static_cast<float>(_difop_pkt.top_vbus / 100.0);
    top_sys_3v8_            = static_cast<float>(_difop_pkt.top_sys_3v8 / 100.0);
    top_sys_3v3_            = static_cast<float>(_difop_pkt.top_sys_3v3 / 100.0);
    top_sys_2v5_            = static_cast<float>(_difop_pkt.top_sys_2v5 / 100.0);
    top_sys_1v1_            = static_cast<float>(_difop_pkt.top_sys_1v1 / 100.0);
    top_rx_vbd_             = static_cast<float>(_difop_pkt.top_rx_vbd / 100.0);
    top_tx_charge_          = static_cast<float>(_difop_pkt.top_tx_charge / 100.0);
    top_sys_1v8_            = static_cast<float>(_difop_pkt.top_sys_1v8 / 100.0);
    top_sys_1v0_            = static_cast<float>(_difop_pkt.top_sys_1v0 / 100.0);
    bot_psintlp_            = static_cast<float>(_difop_pkt.bot_psintlp / 100.0);
    bot_psintfp_            = static_cast<float>(_difop_pkt.bot_psintfp / 100.0);
    bot_ps_aux_             = static_cast<float>(_difop_pkt.bot_ps_aux / 100.0);
    bot_pl_vccint_          = static_cast<float>(_difop_pkt.bot_pl_vccint / 100.0);
    total_power_            = static_cast<float>(_difop_pkt.total_power / 100.0);
    imu_qx_                 = _difop_pkt.q_x;
    imu_qy_                 = _difop_pkt.q_y;
    imu_qz_                 = _difop_pkt.q_z;
    imu_qw_                 = _difop_pkt.q_w;
    imu_x_                  = _difop_pkt.x;
    imu_y_                  = _difop_pkt.y;
    imu_z_                  = _difop_pkt.z;
    bot_soc_temp_           = static_cast<float>(_difop_pkt.bot_soc_temp / 100.0);
    bot_fpd_temp_           = static_cast<float>(_difop_pkt.bot_fpd_temp / 100.0);
    bot_lpd_temp_           = static_cast<float>(_difop_pkt.bot_lpd_temp / 100.0);
    bot_imu_temp_           = static_cast<float>(_difop_pkt.bot_imu_temp / 100.0);
    top_fpga_temp_          = static_cast<float>(_difop_pkt.top_fpga_temp / 100.0);
    top_tx_temp_            = static_cast<float>(_difop_pkt.top_tx_temp / 100.0);
    top_rx_n_temp_          = static_cast<float>(_difop_pkt.top_rx_n_temp / 100.0);
    top_rx_p_temp_          = static_cast<float>(_difop_pkt.top_rx_p_temp / 100.0);
  }

  [[nodiscard]] QDateTime getDateTime() const { return datetime_; }
  void setDateTime(const QDateTime& _datetime) { datetime_ = _datetime; }

  [[nodiscard]] bool isValid() const { return datetime_.isValid(); }

private:
  QDateTime datetime_;
};
}  // namespace airy_pld

}  // namespace lidar
}  // namespace robosense

#endif  // DATA_STRUCT_H
