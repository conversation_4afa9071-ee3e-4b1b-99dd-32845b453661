﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "mech_communication/protocol/data_struct/airy_pld.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QHostAddress>
#include <QObject>
#include <QString>
#include <QVariant>
#include <qdatetime.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
enum class ActionState
{
  CHECK_MES,                     // 检查MES
  AGING_CONNECT_LIDAR,           // 老化连接雷达
  FIRMWARE_UPDATE_UNZIP,         // 固件升级解压文件
  FIRMWARE_UPDATE_APP,           // 固件升级APP
  FIRMWARE_UPDATE_BOT,           // 固件升级底板
  FIRMWARE_UPDATE_TOP,           // 固件升级顶板
  FIRMWARE_UPDATE_WRITE_CONFIG,  // 固件升级写入配置
  FIRMWARE_UPDATE_REBOOT,        // 固件升级重启
  FIRMWARE_UPDATE_CHECK,         // 固件升级校验
  CLEAR_CALIB_DATA,              // 清除标定数据
  CHANGE_LIDAR_IP,               // 修改雷达IP
  ENCODING_CALIB_MOTOR_STABLE,   // 码盘标定就绪，等待电机稳定
  ENCODING_CALIB_COLLECT,        // 码盘标定采集数据
  ENCODING_CALIB_PROCESS_WRITE,  // 码盘标定计算
  ENCODING_CALIB_WRITE,          // 码盘标定写入寄存器固化
  CHN_ANGLE_WRITE,               // 角度写入
  VBD_CALIB,                     // VBD标定
  INIT_LIDAR,                    // 初始化雷达
  INIT_READ_DATA,                // 读取数据前的初始化
  READ_DATA,                     // 读取数据并校验检查
  DEINIT_READ_DATA,              // 读取数据后的反初始化
  STRESS_TEST,                   // 压测
  RESTORE_LIDAR_IP,              // 恢复雷达IP
  COOLING,                       // 冷却
  FAIL,                          // 失败
  ABORT,                         // 中断
  SUCCESS,                       // 成功
  FINAL,                         // 结束
  END = -1
};
enum EncodeCalibState
{
  ENCODE_CALIB_IDLE,
  ENCODE_CALIB_WAIT,
  ENCODE_CALIB_BUSY,
  ENCODE_CALIB_COLLECT,
  ENCODE_CALIB_PASS,
  ENCODE_CALIB_NG,
  ENCODE_CALIB_SKIP,
  ENCODE_CALIB_ABORT
};
enum FirmwareUpdateState
{
  FIRMWARE_UPDATE_IDLE,
  FIRMWARE_UPDATE_WAIT,
  FIRMWARE_UPDATE_BUSY,
  FIRMWARE_UPDATE_UNZIP,
  FIRMWARE_UPDATE_APP,
  FIRMWARE_UPDATE_BOT,
  FIRMWARE_UPDATE_TOP,
  FIRMWARE_UPDATE_WRITE_CONFIG,
  FIRMWARE_UPDATE_REBOOT,
  FIRMWARE_UPDATE_CHECK,
  FIRMWARE_UPDATE_PASS,
  FIRMWARE_UPDATE_NG,
  FIRMWARE_UPDATE_SKIP,
  FIRMWARE_UPDATE_ABORT
};
enum AgingState
{
  AGING_IDLE,
  AGING_WAIT,
  AGING_BUSY,
  AGING_COOLING,
  AGING_PASS,
  AGING_NG,
  AGING_SKIP,
  AGING_ABORT
};
enum StressState
{
  STRESS_IDLE,
  STRESS_WAIT,
  STRESS_BUSY,
  STRESS_PASS,
  STRESS_NG,
  STRESS_SKIP,
  STRESS_ABORT
};

enum RunState
{
  RUN_IDLE,
  RUN_BUSY,
  RUN_PASS,
  RUN_NG,
  RUN_ABORT
};

enum class ConnectionStatus
{
  UNINITIALIZED,
  CONNECTED,
  DISCONNECTED,
  LAGGING
};

struct ParaInfo
{
  int lidar_index;
  int row_num;
  int col_num;

  QString org_ip     = "*************";
  int org_msop_port  = 6699;
  int org_difop_port = 7788;

  bool fsm_change_ip;
  bool fsm_firmware_update;
  bool fsm_clear_calib_data;
  bool fsm_encoding_calib;
  bool fsm_chn_angle_write;
  bool fsm_vbd_calib;
  bool fsm_stress_test;
  bool fsm_aging;

  QString firmware_dir;
  bool is_use_zip_file;
  QString firmware_zip_file_path;

  int ping_timeout;
  int start_up_max_time;

  bool is_maintain_fail_env;

  // aging para
  int aging_time_secs;
  int aging_check_interval;
  int difop_timeout;
  int cooling_time_secs;

  // 监控msop数量
  int check_msop_size;

  // 码盘标定参数
  int encod_calib_collect_num;

  // VBD标定参数
  float vbd_cal_k;

  // 需要压测的次数
  int stress_num;
  // 压测重新上电时间
  int stress_toggle_interval;
  // 压测上电后等待的时间
  int stress_wait_start_time;
  // // 压测时候雷达转速
  // int stress_motor_speed;
  // 压测允许失败
  bool stress_allow_fail;
};

constexpr uint32_t ADDR_COEFF     = 0x83c05000;
constexpr uint32_t ADDR_STEP      = 0x83c05400;
constexpr uint32_t ADDR_INFO_LOCK = 0x83c04300;
constexpr uint32_t ADDR_SCALE     = 0x83c05800;
constexpr size_t ENCODE_NUM       = 99;

}  // namespace lidar
}  // namespace robosense

#endif  // DATA_STRUCT_H
