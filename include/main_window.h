﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef XTO1_CALIB_MAIN_WINDOW_H
#define XTO1_CALIB_MAIN_WINDOW_H
#include "QJsonObject"
#include "common_struct.h"
#include "mes_widget.h"
#include "message_browser.h"
#include "parameter_setting.h"
#include "tricolor_light_controller.h"
#include <QSet>
#include <QSharedPointer>
#include <QString>
#include <QVariant>
#include <QtWidgets/QMainWindow>
#include <dirent.h>
#include <qglobal.h>
#include <unistd.h>

namespace robosense
{
namespace lidar
{
class AgingProgressWidget;
constexpr int G_MAX_AGING_LIDAR_NUM = 32;

class AgingTableWidget : public QTableWidget
{
  Q_OBJECT
public:
  AgingTableWidget(QWidget* _parent = nullptr) { this->setParent(_parent); };

protected:
  virtual void keyPressEvent(QKeyEvent* _event) override
  {
    if (_event->type() == QKeyEvent::KeyPress)
    {
      if (_event->key() == Qt::Key_Return || _event->key() == Qt::Key_Enter)
      {
        int row = this->currentRow();
        int col = this->currentColumn();
        signalPressReturnKey(row, col);
      }
    }
    QTableWidget::keyPressEvent(_event);
  }

Q_SIGNALS:
  void signalPressReturnKey(int _row, int _col);
};

class WidgetAbout;
class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(int _argc, char** _argv, QWidget* _parent = 0);
  ~MainWindow();

protected:
  void closeEvent(QCloseEvent* _event);
  void moveEvent(QMoveEvent* _event);
  void resizeEvent(QResizeEvent* _event);

public:
  void agingListWidgetInit();
  ParaInfo getParaInfo();
  void relayPortInit();

Q_SIGNALS:
  void signalExitAgingWidget();

protected Q_SLOTS:
  void cleanBeforeQuit();
  void slotShowSetting();
  void slotShowMESWidget();
  void slotShowMessageBox(const QString& _msg);
  void slotShowAbout();
  void slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority = nullptr);
  void slotAgingCounts();
  void slotRightClickedAgingListWidget(QPoint _pt);
  void slotClickedMenuResetLidarStatus(int _lidar_index);
  void slotShowRunningWidget(int _row, int _col);
  void slotLidarChangeIpIsReady(const bool _is_ready, const int _lidar_index);
  void slotClickedMenuShowAgingData(int _lidar_index);
  void slotUpdateParaInfo(const int _lidar_index);
  void slotHumanConfirmResult(const QString& _ver_file_path, HumanConfirmResult* _msg_result);

  void slotFsmStarted(const int _lidar_index);
  void slotFsmStopped(const int _lidar_index);

private:
  void setupLayout();
  void readSettings();
  void writeSettings();
  void registerAgingPara();
  void loadCsvAndAppConfig();
  static void loadCsvLimit(const QString& _root_path);
  void loadAppConfig(const QString& _root_path);

private:
  // ui element
  QRect geometry_;
  int window_index_            = 1;
  bool is_window_position_fix_ = false;
  bool is_window_max_          = false;

  int aging_busy_counts_ = 0;
  int aging_num_         = 0;
  QJsonObject app_config_;

  // state layout
  WidgetAbout* widget_about_                      = nullptr;
  rsfsc_lib::MessageBrowser* browser_message_     = nullptr;
  MesWidget* mes_widget_                          = nullptr;
  AgingTableWidget* aging_list_widget_            = nullptr;
  QLabel* version_label_                          = nullptr;
  QLabel* aging_count_                            = nullptr;
  int running_count_                              = 0;
  ParameterSetting* para_setting_                 = nullptr;
  QSharedPointer<QSet<QString>> aging_sn_set_ptr_ = nullptr;
  std::shared_ptr<TricolorLightController> light_controller_;

  robosense::lidar::rsfsc_lib::UserAuthority* user_authority_ = nullptr;

  std::array<AgingProgressWidget*, G_MAX_AGING_LIDAR_NUM> aging_progress_array_ = { nullptr };
};
}  // namespace lidar
}  // namespace robosense

#endif  // _XTO1_CALIB_MAIN_WINDOW_H_
